(function (cjs, an) {

var p; // shortcut to reference prototypes
var lib={};var ss={};var img={};
lib.ssMetadata = [
		{name:"KV_atlas_1", frames: [[804,0,644,403],[1774,617,187,381],[1612,753,84,54],[0,575,681,222],[683,780,681,222],[1450,0,322,661],[804,405,597,373],[1774,0,267,318],[1774,320,248,295],[1612,663,127,88],[0,799,302,126],[0,0,802,573],[683,575,90,188],[1963,617,68,240],[1520,663,90,188],[1403,663,115,362]]}
];


(lib.AnMovieClip = function(){
	this.actionFrames = [];
	this.ignorePause = false;
	this.gotoAndPlay = function(positionOrLabel){
		cjs.MovieClip.prototype.gotoAndPlay.call(this,positionOrLabel);
	}
	this.play = function(){
		cjs.MovieClip.prototype.play.call(this);
	}
	this.gotoAndStop = function(positionOrLabel){
		cjs.MovieClip.prototype.gotoAndStop.call(this,positionOrLabel);
	}
	this.stop = function(){
		cjs.MovieClip.prototype.stop.call(this);
	}
}).prototype = p = new cjs.MovieClip();
// symbols:



(lib.CachedBmp_51 = function() {
	this.initialize(ss["KV_atlas_1"]);
	this.gotoAndStop(0);
}).prototype = p = new cjs.Sprite();



(lib.CachedBmp_50 = function() {
	this.initialize(ss["KV_atlas_1"]);
	this.gotoAndStop(1);
}).prototype = p = new cjs.Sprite();



(lib.CachedBmp_49 = function() {
	this.initialize(ss["KV_atlas_1"]);
	this.gotoAndStop(2);
}).prototype = p = new cjs.Sprite();



(lib.CachedBmp_48 = function() {
	this.initialize(ss["KV_atlas_1"]);
	this.gotoAndStop(3);
}).prototype = p = new cjs.Sprite();



(lib.CachedBmp_47 = function() {
	this.initialize(ss["KV_atlas_1"]);
	this.gotoAndStop(4);
}).prototype = p = new cjs.Sprite();



(lib.CachedBmp_46 = function() {
	this.initialize(ss["KV_atlas_1"]);
	this.gotoAndStop(5);
}).prototype = p = new cjs.Sprite();



(lib.CachedBmp_45 = function() {
	this.initialize(ss["KV_atlas_1"]);
	this.gotoAndStop(6);
}).prototype = p = new cjs.Sprite();



(lib.CachedBmp_44 = function() {
	this.initialize(img.CachedBmp_44);
}).prototype = p = new cjs.Bitmap();
p.nominalBounds = new cjs.Rectangle(0,0,3671,812);


(lib.CachedBmp_43 = function() {
	this.initialize(ss["KV_atlas_1"]);
	this.gotoAndStop(7);
}).prototype = p = new cjs.Sprite();



(lib.CachedBmp_42 = function() {
	this.initialize(ss["KV_atlas_1"]);
	this.gotoAndStop(8);
}).prototype = p = new cjs.Sprite();



(lib.CachedBmp_41 = function() {
	this.initialize(ss["KV_atlas_1"]);
	this.gotoAndStop(9);
}).prototype = p = new cjs.Sprite();



(lib.CachedBmp_40 = function() {
	this.initialize(ss["KV_atlas_1"]);
	this.gotoAndStop(10);
}).prototype = p = new cjs.Sprite();



(lib.CachedBmp_39 = function() {
	this.initialize(ss["KV_atlas_1"]);
	this.gotoAndStop(11);
}).prototype = p = new cjs.Sprite();



(lib.CachedBmp_38 = function() {
	this.initialize(ss["KV_atlas_1"]);
	this.gotoAndStop(12);
}).prototype = p = new cjs.Sprite();



(lib.CachedBmp_37 = function() {
	this.initialize(ss["KV_atlas_1"]);
	this.gotoAndStop(13);
}).prototype = p = new cjs.Sprite();



(lib.CachedBmp_36 = function() {
	this.initialize(ss["KV_atlas_1"]);
	this.gotoAndStop(14);
}).prototype = p = new cjs.Sprite();



(lib.CachedBmp_35 = function() {
	this.initialize(ss["KV_atlas_1"]);
	this.gotoAndStop(15);
}).prototype = p = new cjs.Sprite();
// helper functions:

function mc_symbol_clone() {
	var clone = this._cloneProps(new this.constructor(this.mode, this.startPosition, this.loop, this.reversed));
	clone.gotoAndStop(this.currentFrame);
	clone.paused = this.paused;
	clone.framerate = this.framerate;
	return clone;
}

function getMCSymbolPrototype(symbol, nominalBounds, frameBounds) {
	var prototype = cjs.extend(symbol, cjs.MovieClip);
	prototype.clone = mc_symbol_clone;
	prototype.nominalBounds = nominalBounds;
	prototype.frameBounds = frameBounds;
	return prototype;
	}


(lib.貨車俯 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// 圖層_1
	this.shape = new cjs.Shape();
	this.shape.graphics.f().s("#FFFFFF").ss(0.4,0,0,4).p("AiQB8ICeBCICDk5IifhCg");
	this.shape.setTransform(16.1,33.6);

	this.shape_1 = new cjs.Shape();
	this.shape_1.graphics.f().s("#194FBA").ss(0.1,0,0,4).p("ACIikIAEgKIAfgmIg8CBIgGgDIgCgBIAEgKIhggoIAhhNIBgAoIgEAKIgdBDAAxkHIAJgFIBuAuIADAKAAsjWIAFgxIB6AzAAGh/IBjAqIAYAKIANAZIgYA5IgUAwIgoBgIgUAwIgdBGIi5hOIAdhGIAUgvIAohgIAUgvIAYg7IARgMIAaAKIAAACIAGACIAAgBIAFgKACOgyIi5hOAAAiCIAxiF");
	this.shape_1.setTransform(17.7115,26.9466);

	this.shape_2 = new cjs.Shape();
	this.shape_2.graphics.f().s("#194FBA").ss(0.2,0,0,60).p("AAAAAIAAAA");
	this.shape_2.setTransform(31.6875,9.4);

	this.shape_3 = new cjs.Shape();
	this.shape_3.graphics.rf(["#FFFFFF","#A0C0F2"],[0,1],0,0,0,0,0,3.5).s().p("AgNAhQgNgGgGgNQgFgOAFgMQAGgOANgGQANgFANAFQAOAGAGAOQAFAMgFAOQgGANgOAGQgGACgHAAQgGAAgHgCgAgEgMQgFACgCAFQgDAFACAFQACAFAFACQAFADAEgCQAGgBACgGQADgFgCgEQgBgGgGgCIgGgCIgEABg");
	this.shape_3.setTransform(15.25,45.95);

	this.shape_4 = new cjs.Shape();
	this.shape_4.graphics.rf(["#FFFFFF","#A0C0F2"],[0,1],0,0,0,0,0,3.5).s().p("AgNAgQgNgFgFgOQgGgNAGgNQAFgNAOgFQAMgGANAGQAOAFAFAOQAGAMgGANQgFAOgOAFQgHADgGAAQgGAAgHgDgAgEgMQgFACgDAFQgCAFACAEQACAGAFACQAFADAEgCQAGgCACgFQADgFgCgEQgCgGgFgCQgDgCgDAAIgEABg");
	this.shape_4.setTransform(7.975,42.925);

	this.shape_5 = new cjs.Shape();
	this.shape_5.graphics.rf(["#FFFFFF","#A0C0F2"],[0,1],0,0,0,0,0,3.5).s().p("AgNAgQgNgFgGgOQgFgNAFgNQAGgNANgFQANgGANAGQAOAFAFAOQAGAMgGANQgFAOgOAFQgGADgHAAQgGAAgHgDgAgEgMQgFACgDAFQgCAFACAEQABAGAGACQAFADAEgCQAGgCACgFQADgFgCgFQgCgFgFgDIgGgBIgEABg");
	this.shape_5.setTransform(18.2,38.875);

	this.shape_6 = new cjs.Shape();
	this.shape_6.graphics.rf(["#FFFFFF","#A0C0F2"],[0,1],0,0,0,0,0,3.5).s().p("AgNAhQgNgGgFgNQgGgOAGgMQAFgOAOgFQAMgGANAGQAOAFAFAOQAGAMgGAOQgFANgOAFQgHADgGAAQgGAAgHgCgAgEgMQgFACgDAFQgCAFACAFQACAFAFADQAFACAEgCQAGgCACgFQADgFgCgEQgCgGgFgCIgGgCIgEABg");
	this.shape_6.setTransform(10.925,35.844);

	this.shape_7 = new cjs.Shape();
	this.shape_7.graphics.rf(["#FFFFFF","#A0C0F2"],[0,1],0,0,0,0,0,3.5).s().p("AgNAhQgNgGgFgOQgGgNAGgNQAFgNANgGQANgFANAFQAOAGAFANQAGANgGANQgFAOgOAGQgGACgHAAQgGAAgHgCgAgEgMQgFABgDAGQgCAFACAEQACAGAFACQAFADAEgCQAGgBACgGQADgFgCgFQgCgFgFgCIgGgCIgEABg");
	this.shape_7.setTransform(13.875,28.75);

	this.shape_8 = new cjs.Shape();
	this.shape_8.graphics.rf(["#FFFFFF","#A0C0F2"],[0,1],0,0,0,0,0,3.5).s().p("AgNAgQgNgFgFgNQgGgOAGgMQAFgOANgFQANgGAOAGQANAFAFAOQAGAMgGAOQgFANgNAFQgHADgHAAQgGAAgHgDgAgEgMQgFACgDAFQgCAFACAFQACAFAFADQAFACAFgCQAFgCADgFQACgFgCgEQgCgGgFgCIgGgCIgEABg");
	this.shape_8.setTransform(21.15,31.775);

	this.shape_9 = new cjs.Shape();
	this.shape_9.graphics.rf(["#FFFFFF","#A0C0F2"],[0,1],0,0,0,0,0,3.5).s().p("AgNAgQgNgFgFgOQgGgNAFgNQAGgNANgFQANgGANAGQAOAFAFAOQAGAMgGANQgFAOgOAFQgGADgHAAQgGAAgHgDgAgEgMQgFACgDAFQgCAFACAFQACAFAFACQAFADAEgCQAGgCACgFQADgFgCgEQgCgGgFgCQgDgCgDAAIgEABg");
	this.shape_9.setTransform(16.8185,21.675);

	this.shape_10 = new cjs.Shape();
	this.shape_10.graphics.rf(["#FFFFFF","#A0C0F2"],[0,1],0,0,0,0,0,3.5).s().p("AgMAhQgOgGgFgOQgGgNAGgNQAFgNAOgGQAMgFAOAFQANAGAGANQAFANgFANQgGAOgNAGQgHACgHAAQgGAAgGgCgAgDgMQgGACgCAFQgDAFACAEQACAGAFACQAFADAFgCQAFgBADgGQACgFgCgFQgBgFgGgCIgGgCIgDABg");
	this.shape_10.setTransform(24.1,24.7);

	this.shape_11 = new cjs.Shape();
	this.shape_11.graphics.f("#C5DDFF").s().p("AAUBXIgCgBIAEgKIAdhDIAEgKIAfglIg8CAgAhVAtIAAgCIAyiEIgFAxIghBMIgEAKIAAABg");
	this.shape_11.setTransform(26.2,9.55);

	this.shape_12 = new cjs.Shape();
	this.shape_12.graphics.f("#194FBA").s().p("AiADZIAGgOIBhAoIgGAOgAibC1ICCk5ICfBDIiDE4gAggCUQgOAGgFAOQgGANAGAOQAFANAOAGQANAFAOgFQAMgGAGgNQAFgOgFgNQgGgOgMgGQgHgCgHAAQgHAAgGACgAhpB2QgNAGgGANQgFAOAFANQAGANANAGQANAFAOgFQANgGAGgNQAFgNgFgOQgGgNgNgGQgHgCgGAAQgHAAgHACgAgDBOQgNAGgGANQgFANAFAOQAGANANAGQANAFANgFQAOgGAFgNQAGgNgGgOQgFgOgOgFQgHgDgGAAQgHAAgGADgAhLAvQgOAGgFANQgGAOAGANQAFAOANAFQAOAGANgGQAOgGAFgNQAGgNgGgOQgFgNgOgGQgGgCgHAAQgGAAgHACgAAaAHQgOAGgFANQgGANAGAOQAFAOAOAFQANAFAOgFQANgGAGgNQAFgOgFgNQgGgNgNgGQgHgDgHAAQgHAAgGADgAgugXQgNAGgGANQgFANAFANQAGAOANAGQANAFAOgFQANgGAGgOQAEgNgEgNQgGgNgNgGQgHgCgHAAQgGAAgHACgAA3g/QgNAGgGANQgFANAFAOQAGANANAFQAOAFANgFQAOgFAFgNQAGgOgGgNQgFgNgOgGQgGgDgHAAQgHAAgHADgAgRhdQgNAFgGAOQgFANAGAOQAFANANAGQAOAFAMgFQAOgGAFgNQAGgNgGgOQgFgOgOgFQgGgDgGAAQgHAAgHADgAArC+IAUgwQAFAEADAHQAEAKgEAKQgEALgKAFQgFABgGAAIgDAAgAiWBmQgEgKAEgKQAFgLAKgEQAHgDAGABIgUAvQgFgEgDgGgABnAuIAUgvQAFADADAHQAEAKgEAKQgEALgKAEQgGACgFAAIgDAAgAhagpQgEgLAEgKQAFgKAKgFQAGgCAHAAIgUAwQgFgEgDgGgAALiIIAFgKIBgAoIgEAKIACABIAAAAgABEjjIgBgdIBZAlIgUAXg");
	this.shape_12.setTransform(17.225,27.85);

	this.shape_13 = new cjs.Shape();
	this.shape_13.graphics.f("#FFFFFF").s().p("AitDAIAdhGIAUgwIAohfIAUgwIAYg7IARgMIAZALIAAABIAIACIAAAAIBiApIAYAKIAOAZIi6hOIC6BOIgZA5IgUAxIgoBfIgUAxIgdBFgAidC/ICeBCICDk5IiehCgAgaDLQgGgDgCgFQgCgGADgEQADgFAFgDQAGgCAEADQAGACACAGQACAGgDAEQgCAGgGACIgFAAIgFgBgAhjCtQgGgDgBgFQgCgGACgFQADgFAFgCQAGgCAFADQAFACACAGQABAFgCAGQgCAFgGACIgEAAIgGgBgAACCEQgFgDgBgFQgCgGACgEQADgFAEgDQAGgBAFACQAFACACAGQACAGgDAFQgCAFgGACIgEABQgDAAgDgCgAhGBmQgFgCgCgGQgCgFACgGQADgFAFgBQAHgCAEACQAGADABAFQACAGgDAFQgCAEgGADIgEAAIgGgBgAAfA9QgFgCgBgGQgDgGADgFQADgEAFgCQAFgCAGACQAFADABAFQADAGgDAFQgDAFgFACIgFABIgGgCgAgpAfQgEgCgDgGQgBgGACgEQACgGAGgCQAGgCAFADQAFADACAFQABAGgCAEQgDAGgFACIgFAAIgGgBgAA9gJQgFgCgCgGQgCgFADgFQACgFAGgCQAFgCAFADQAGACABAFQACAGgCAFQgDAGgFABIgFABIgGgCgAgLgnQgFgCgCgGQgCgFADgFQACgFAGgDQAFgBAFACQAFADACAFQABAGgCAFQgDAFgEACIgEABIgHgCgAAOiIIAhhOIAFgxIAJgFIBuAvIADAKIh6g0IB6A0IgfAlIAAAAIhggoIBgAoIgEAKIgdBEgABCjaIBEAgIAUgYIhZgkgACujTg");
	this.shape_13.setTransform(17.4,26.9);

	this.timeline.addTween(cjs.Tween.get({}).to({state:[{t:this.shape_13},{t:this.shape_12},{t:this.shape_11},{t:this.shape_10},{t:this.shape_9},{t:this.shape_8},{t:this.shape_7},{t:this.shape_6},{t:this.shape_5},{t:this.shape_4},{t:this.shape_3},{t:this.shape_2},{t:this.shape_1},{t:this.shape}]}).wait(1));

	this._renderFirstFrame();

}).prototype = getMCSymbolPrototype(lib.貨車俯, new cjs.Rectangle(-1,-1,36.8,55.8), null);


(lib.補間動畫5 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// 圖層_1
	this.shape = new cjs.Shape();
	this.shape.graphics.f().s("#194FBA").ss(1,0,0,4).p("AhoAGIAAg0IBEgUQgEgMAEgMQAIgZAngEIARAJQAQAPgHAdIA/AAIAFCVIjRAlIAAhXIAAgbIDJgjIADAXIjMAn");
	this.shape.setTransform(0.0363,0.0828,1.3348,1.3348);

	this.shape_1 = new cjs.Shape();
	this.shape_1.graphics.f("#FFFFFF").s().p("AhoAhIAAgbIDJgjIADAXIjMAnIDMgnIgDgXIjJAjIAAg0IBEgUQgDgLADgNQAJgZAmgEIARAJQAQAPgHAdIA/AAIAFCVIjRAlg");
	this.shape_1.setTransform(0.0278,0.0274,1.3348,1.3348);

	this.timeline.addTween(cjs.Tween.get({}).to({state:[{t:this.shape_1},{t:this.shape}]}).wait(1));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-15,-17,30.1,34.1);


(lib.補間動畫3 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// 圖層_1
	this.shape = new cjs.Shape();
	this.shape.graphics.f().s("#194FBA").ss(1,0,0,4).p("AhoAGIAAg0IBEgUQgEgMAEgMQAIgZAngEIARAJQAQAPgHAdIA/AAIAFCVIjRAlIAAhXgAhoAhIDMgnIgDgXIjJAj");
	this.shape.setTransform(0.0363,0.0828,1.3348,1.3348);

	this.shape_1 = new cjs.Shape();
	this.shape_1.graphics.f("#FFFFFF").s().p("AhoAhIDMgnIgDgXIjJAjIAAg0IBEgUQgDgLADgNQAJgZAmgEIARAJQAQAPgHAdIA/AAIAFCVIjRAlgAhoAGIDJgjIADAXIjMAng");
	this.shape_1.setTransform(0.0278,0.0274,1.3348,1.3348);

	this.timeline.addTween(cjs.Tween.get({}).to({state:[{t:this.shape_1},{t:this.shape}]}).wait(1));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-15,-17,30.1,34.1);


(lib.箭頭 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// timeline functions:
	this.frame_7 = function() {
		/* stop();
		*/
	}

	// actions tween:
	this.timeline.addTween(cjs.Tween.get(this).wait(7).call(this.frame_7).wait(3));

	// 圖層_2 (mask)
	var mask = new cjs.Shape();
	mask._off = true;
	var mask_graphics_0 = new cjs.Graphics().p("AhDh1IBFgTIBCD+IhFATg");
	var mask_graphics_1 = new cjs.Graphics().p("AhkhiIBmgwIBjEDIhqAig");
	var mask_graphics_2 = new cjs.Graphics().p("AiFhOICGhPICGEIIiPAzg");
	var mask_graphics_3 = new cjs.Graphics().p("Aimg7ICmhtICoEOIizBDg");
	var mask_graphics_4 = new cjs.Graphics().p("AjIgnIDIiMIDJETIjYBUg");
	var mask_graphics_5 = new cjs.Graphics().p("AjpgUIDpipIDqEYIj8Bjg");
	var mask_graphics_6 = new cjs.Graphics().p("AkKAAIEJjIIEMEdIkhB0g");
	var mask_graphics_7 = new cjs.Graphics().p("AkrASIEqjlIEtEjIlFCEg");

	this.timeline.addTween(cjs.Tween.get(mask).to({graphics:mask_graphics_0,x:35.725,y:-0.375}).wait(1).to({graphics:mask_graphics_1,x:32.425,y:0.175}).wait(1).to({graphics:mask_graphics_2,x:29.1,y:0.725}).wait(1).to({graphics:mask_graphics_3,x:25.8,y:1.275}).wait(1).to({graphics:mask_graphics_4,x:22.475,y:1.825}).wait(1).to({graphics:mask_graphics_5,x:19.175,y:2.375}).wait(1).to({graphics:mask_graphics_6,x:15.85,y:2.925}).wait(1).to({graphics:mask_graphics_7,x:12.55,y:3.475}).wait(3));

	// 圖層_1
	this.shape = new cjs.Shape();
	this.shape.graphics.f("#FF9900").s().p("AhhgUIARAaQBuhhBsAIIACATQh6AMhPBNIAYAZQgagChMAlQAxhegHgLg");
	this.shape.setTransform(17.3,3.95,1,1,0,0,0,3.3,-4.5);

	var maskedShapeInstanceList = [this.shape];

	for(var shapedInstanceItr = 0; shapedInstanceItr < maskedShapeInstanceList.length; shapedInstanceItr++) {
		maskedShapeInstanceList[shapedInstanceItr].mask = mask;
	}

	this.timeline.addTween(cjs.Tween.get(this.shape).wait(10));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(0,0,28,17);


(lib.空貨車 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// 圖層_1
	this.instance = new lib.CachedBmp_47();
	this.instance.setTransform(0,-0.45,0.3582,0.3582);

	this.instance_1 = new lib.CachedBmp_48();
	this.instance_1.setTransform(0,-1.45,0.3582,0.3582);

	this.timeline.addTween(cjs.Tween.get({}).to({state:[{t:this.instance}]}).to({state:[{t:this.instance_1}]},1).wait(1));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(0,-1.4,244,80.5);


(lib.數 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// 圖層_1
	this.shape = new cjs.Shape();
	this.shape.graphics.f().s("#194FBA").ss(1,0,0,4).p("AAAk7QAXgPAVAwIBSFHIAFAMQADAOgFAJQgRAehhgbIAKDsIgmAAIAAjnQgjAJgfgBQhAgBAPgtIBvlVg");
	this.shape.setTransform(13.2138,31.9568);

	this.timeline.addTween(cjs.Tween.get(this.shape).wait(1));

	this._renderFirstFrame();

}).prototype = getMCSymbolPrototype(lib.數, new cjs.Rectangle(-1,-1,28.4,65.9), null);


(lib.海水 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// 圖層_1
	this.shape = new cjs.Shape();
	this.shape.graphics.f().s("#194FBA").ss(1,0,0,4).p("AMpgxQgEgbAPgUQAQgWAhgGQAhgGAeANQAiAOAPAgQASAlgKAkQgNAsg2AWAKogcQAFAMgMANQgBgNAIgMgAvCCNQgZgEgEgeQgEgdAUgPQARgNAaAHQAcAGgBAXQACgoA/gLQA+gLATAhQgJgqArgZQAngZA9gBQA+AAAtAaQAyAcAFA1QApgrBQgHQBYgHAWA7QAKgqBGgKQBGgJAYAiQAOguBGgIQBGgKAbAnQgLg+AygsQArgnBEgKQA/gKAnAGQA0AIAlAnQATAUAGARQAJAYgMAeQARgZAfgRQAdgRAggGQAbgFAjAUQATALAEALQAFgGAHgGQATgQAegGQAdgHAUAGQAOAFAFAJQADAGgBAI");
	this.shape.setTransform(99.2601,14.122);

	this.shape_1 = new cjs.Shape();
	this.shape_1.graphics.f("#FFFFFF").s().p("AvCBFQgZgEgEgeQgEgeAUgNQARgNAaAGQAcAGgBAWQACgmA/gLQA+gLATAhQgJgqArgbQAngZA9AAQA+gBAtAaQAyAeAFA1QApgsBQgGQBYgHAWA5QAKgpBGgJQBGgKAYAiQAOguBGgJQBGgJAbAoQgLhAAygsQArgmBEgLQA/gKAnAHQA0AIAlAmQATAUAGASQAJAYgMAdQARgYAfgSQAdgRAggGQAbgEAjAUQATAKAEAMQAFgHAHgGQATgPAegHQAdgGAUAGQANAEAGAKQgEgbAPgUQAQgWAhgHQAhgGAeANQAiAPAPAgQASAkgKAlQgNAtg2AWQoXDZoFAAQmmAAmaiRg");
	this.shape_1.setTransform(99.2601,21.3529);

	this.timeline.addTween(cjs.Tween.get({}).to({state:[{t:this.shape_1},{t:this.shape}]}).wait(1));

	this._renderFirstFrame();

}).prototype = getMCSymbolPrototype(lib.海水, new cjs.Rectangle(-1,-1,200.6,43.7), null);


(lib.片段群組0 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// 圖層_2 (mask)
	var mask = new cjs.Shape();
	mask._off = true;
	mask.graphics.p("AiOErQhPgnguhLQgxhNAAhbQAAiCBdhdQBdhcCCAAQCDAABcBcQBeBdAACCQgBBbgwBNQgvBLhPAng");
	mask.setTransform(39.05,40.875);

	// 圖層_3
	this.instance = new lib.CachedBmp_43();
	this.instance.setTransform(0,0,0.2794,0.2794);

	var maskedShapeInstanceList = [this.instance];

	for(var shapedInstanceItr = 0; shapedInstanceItr < maskedShapeInstanceList.length; shapedInstanceItr++) {
		maskedShapeInstanceList[shapedInstanceItr].mask = mask;
	}

	this.timeline.addTween(cjs.Tween.get(this.instance).wait(1));

	this._renderFirstFrame();

}).prototype = getMCSymbolPrototype(lib.片段群組0, new cjs.Rectangle(7.4,11,63.300000000000004,59.8), null);


(lib.片段群組0_1 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// 圖層_2 (mask)
	var mask_1 = new cjs.Shape();
	mask_1._off = true;
	mask_1.graphics.p("AiOErQhPgnguhLQgxhNAAhbQAAiCBdhdQBdhcCCAAQCDAABcBcQBeBdAACCQgBBbgwBNQgvBLhPAng");
	mask_1.setTransform(39.05,40.875);

	// 圖層_3
	this.instance_1 = new lib.CachedBmp_42();
	this.instance_1.setTransform(0,0,0.3014,0.3014);

	var maskedShapeInstanceList = [this.instance_1];

	for(var shapedInstanceItr = 0; shapedInstanceItr < maskedShapeInstanceList.length; shapedInstanceItr++) {
		maskedShapeInstanceList[shapedInstanceItr].mask = mask_1;
	}

	this.timeline.addTween(cjs.Tween.get(this.instance_1).wait(1));

	this._renderFirstFrame();

}).prototype = getMCSymbolPrototype(lib.片段群組0_1, new cjs.Rectangle(7.4,11,63.300000000000004,59.8), null);


(lib.眼睛 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// 圖層_1
	this.instance = new lib.CachedBmp_41();
	this.instance.setTransform(-0.45,-0.45,0.4017,0.4017);

	this.timeline.addTween(cjs.Tween.get(this.instance).wait(1));

	this._renderFirstFrame();

}).prototype = getMCSymbolPrototype(lib.眼睛, new cjs.Rectangle(-0.4,-0.4,51,35.3), null);


(lib.船身 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// 圖層_1
	this.instance = new lib.CachedBmp_39();
	this.instance.setTransform(-0.8,-0.5,0.2684,0.2684);

	this.timeline.addTween(cjs.Tween.get(this.instance).wait(1));

	this._renderFirstFrame();

}).prototype = getMCSymbolPrototype(lib.船身, new cjs.Rectangle(-0.8,-0.5,215.3,153.8), null);


(lib.氣瓶暗 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// 圖層_1
	this.instance = new lib.CachedBmp_38();
	this.instance.setTransform(-0.45,-0.25,0.2684,0.2684);

	this.timeline.addTween(cjs.Tween.get(this.instance).wait(1));

	this._renderFirstFrame();

}).prototype = getMCSymbolPrototype(lib.氣瓶暗, new cjs.Rectangle(-0.4,-0.2,24.099999999999998,50.400000000000006), null);


(lib.氣瓶01 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// 圖層_2
	this.shape = new cjs.Shape();
	this.shape.graphics.f("#006AD8").s().p("AABBtQgogDgRgdQgKgQAAgTQAAgWAOgRQAJgKAIgEIACgBIAAgBQgMgMgFgKQgFgKABgNQAGgyAyAAQAbAAANAQQAKAMABANQgDAegeACQgRAAgGgKQgGgHAAgJQABgMAIgGQAFgEAHgCIACgCQAAAAAAAAQAAgBAAAAQAAAAgBAAQAAgBAAAAQgYgBgJALQgEAFgCANQAAALAIAJQAEAFADACQAGAFAMAGIAXALQALAEAKAKQAQASAAAZQgCAqggAPQgPAHgOAAIgDAAgAgtABIgFAIIAAAJQAAAKAIAFQAFADAFAAQAGAAAGgEQAJgGgBgLQgCgLgGgGIgFgEIgDgCIgDgCIgBgBQgIAEgFAIgAAShWQgIACgFAFIgEAFIAAADIAAAGQAAAEADAEQAFAGAHAAQAHAAAFgGQAEgFAAgHQgBgHgEgFQgDgFgFAAIgBAAg");
	this.shape.setTransform(11.225,50.0806);

	this.timeline.addTween(cjs.Tween.get(this.shape).wait(1));

	// 圖層_1
	this.instance = new lib.CachedBmp_37();
	this.instance.setTransform(-0.5,-0.35,0.3582,0.3582);

	this.timeline.addTween(cjs.Tween.get(this.instance).wait(1));

	this._renderFirstFrame();

}).prototype = getMCSymbolPrototype(lib.氣瓶01, new cjs.Rectangle(-0.5,-0.3,24.4,86), null);


(lib.氣瓶 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// 圖層_1
	this.instance = new lib.CachedBmp_36();
	this.instance.setTransform(-0.45,-0.3,0.2684,0.2684);

	this.timeline.addTween(cjs.Tween.get(this.instance).wait(1));

	this._renderFirstFrame();

}).prototype = getMCSymbolPrototype(lib.氣瓶, new cjs.Rectangle(-0.4,-0.3,24.099999999999998,50.5), null);


(lib.紅綠東 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// 圖層_1
	this.instance = new lib.CachedBmp_35();
	this.instance.setTransform(0,0,0.2686,0.2686);

	this.timeline.addTween(cjs.Tween.get(this.instance).wait(1));

	this._renderFirstFrame();

}).prototype = getMCSymbolPrototype(lib.紅綠東, new cjs.Rectangle(0,0,30.9,97.3), null);


(lib.碼頭 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// timeline functions:
	this.frame_5 = function() {
		/* stop();*/
	}

	// actions tween:
	this.timeline.addTween(cjs.Tween.get(this).wait(5).call(this.frame_5).wait(1));

	// 圖層_1
	this.instance = new lib.補間動畫3("synched",0);
	this.instance.setTransform(14,49);

	this.instance_1 = new lib.補間動畫5("synched",0);
	this.instance_1.setTransform(14,16);

	this.timeline.addTween(cjs.Tween.get({}).to({state:[{t:this.instance}]}).to({state:[{t:this.instance}]},3).to({state:[{t:this.instance_1}]},2).wait(1));
	this.timeline.addTween(cjs.Tween.get(this.instance).to({scaleY:1.5928,y:7.5},3).to({_off:true,scaleY:1,y:16},2).wait(1));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-1,-18.9,30.1,85);


(lib.工廠動態 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// 圖層_1
	this.instance = new lib.片段群組0();
	this.instance.setTransform(73.45,84.4,1,1,0,0,0,37.4,44.6);

	this.instance_1 = new lib.CachedBmp_51();
	this.instance_1.setTransform(0,-0.45,0.2794,0.2794);

	this.timeline.addTween(cjs.Tween.get({}).to({state:[{t:this.instance_1},{t:this.instance}]}).wait(1));

	this._renderFirstFrame();

}).prototype = getMCSymbolPrototype(lib.工廠動態, new cjs.Rectangle(0,-0.4,180,129.1), null);


(lib.工廠 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// 圖層_1
	this.instance = new lib.工廠動態();
	this.instance.setTransform(89.9,64.2,1.0296,1,0,0,0,89.9,64.2);

	this.timeline.addTween(cjs.Tween.get(this.instance).to({scaleX:1.0164},2).to({scaleY:1.0273},2).to({scaleX:1.0296,scaleY:1},2).wait(1));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-2.6,-2.2,185.29999999999998,132.6);


(lib.台灣 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// timeline functions:
	this.frame_64 = function() {
		/* stop();*/
	}

	// actions tween:
	this.timeline.addTween(cjs.Tween.get(this).wait(64).call(this.frame_64).wait(1));

	// 圖層_12
	this.instance = new lib.貨車俯();
	this.instance.setTransform(21.5,171.4,0.4439,0.4439,-27.6186,0,0,17.9,27.2);
	this.instance.alpha = 0;
	this.instance._off = true;

	this.timeline.addTween(cjs.Tween.get(this.instance).wait(37).to({_off:false},0).to({regX:18,scaleX:0.443,scaleY:0.443,rotation:-10.5246,y:145.8,alpha:1},8).to({regX:17.8,regY:27.3,scaleX:0.4428,scaleY:0.4428,rotation:0.4383,x:25.9,y:133.3,alpha:0},6).wait(14));

	// 圖層_9
	this.instance_1 = new lib.貨車俯();
	this.instance_1.setTransform(82.3,22.8,0.4471,0.4471,-102.8325,0,0,17.7,27.1);
	this.instance_1._off = true;

	this.timeline.addTween(cjs.Tween.get(this.instance_1).wait(37).to({_off:false},0).to({scaleX:0.446,scaleY:0.446,rotation:-139.6749,x:65.35,y:24.8},8).to({scaleX:0.4457,scaleY:0.4457,rotation:-152.2076,x:51.1,y:34.7,alpha:0},6).wait(14));

	// 小貨車
	this.instance_2 = new lib.貨車俯();
	this.instance_2.setTransform(68.15,37.3,0.4497,0.4497,-106.5212,0,0,17.4,27.1);
	this.instance_2.alpha = 0;
	this.instance_2._off = true;

	this.timeline.addTween(cjs.Tween.get(this.instance_2).wait(37).to({_off:false},0).to({regX:17.9,scaleX:0.4473,scaleY:0.4473,rotation:-160.7089,x:52.3,y:47.4,alpha:1},8).to({regX:18,regY:26.9,scaleX:0.4469,scaleY:0.4469,rotation:-176.5098,x:41.3,y:64.8,alpha:0},6).wait(14));

	// 眼睛
	this.instance_3 = new lib.眼睛();
	this.instance_3.setTransform(66.75,73.6,1,1,0,0,0,25,17.2);
	this.instance_3.alpha = 0;
	this.instance_3._off = true;

	this.timeline.addTween(cjs.Tween.get(this.instance_3).wait(12).to({_off:false},0).to({alpha:1},3).wait(50));

	// 地標
	this.shape = new cjs.Shape();
	this.shape.graphics.f("#FF900B").s().p("AgvArIAzhVIAsBVg");
	this.shape.setTransform(86.225,21.475);

	this.instance_4 = new lib.CachedBmp_49();
	this.instance_4.setTransform(57.4,17.15,0.4017,0.4017);

	this.instance_5 = new lib.CachedBmp_50();
	this.instance_5.setTransform(16,17.15,0.4017,0.4017);

	this.timeline.addTween(cjs.Tween.get({}).to({state:[]}).to({state:[{t:this.shape}]},15).to({state:[{t:this.instance_4}]},1).to({state:[{t:this.instance_5}]},1).wait(48));

	// 圖層_10
	this.instance_6 = new lib.箭頭("synched",0,false);
	this.instance_6.setTransform(26.1,146.65,1.4412,1.4412,0,-108.5815,71.4185,14,8.5);
	this.instance_6._off = true;

	this.timeline.addTween(cjs.Tween.get(this.instance_6).wait(31).to({_off:false},0).wait(20).to({startPosition:9},0).to({alpha:0,startPosition:0},5).wait(9));

	// 圖層_8
	this.instance_7 = new lib.箭頭("synched",0,false);
	this.instance_7.setTransform(64.8,29.45,1.4441,1.4441,5.0091,0,0,14,8.4);
	this.instance_7._off = true;

	this.timeline.addTween(cjs.Tween.get(this.instance_7).wait(31).to({_off:false},0).wait(20).to({startPosition:9},0).to({alpha:0,startPosition:0},5).wait(9));

	// 箭號
	this.instance_8 = new lib.箭頭("synched",0,false);
	this.instance_8.setTransform(50.4,54.55,1.4419,1.4419,-22.086,0,0,14,8.6);
	this.instance_8._off = true;

	this.timeline.addTween(cjs.Tween.get(this.instance_8).wait(31).to({_off:false},0).wait(20).to({startPosition:9},0).to({alpha:0,startPosition:0},5).wait(9));

	// 圖層_4
	this.shape_1 = new cjs.Shape();
	this.shape_1.graphics.f().s("#194FBA").ss(1,0,0,4).p("AAcAbQgNgRgPgPQgOgMgNgH");
	this.shape_1.setTransform(93.05,3.201);

	this.shape_2 = new cjs.Shape();
	this.shape_2.graphics.f().s("#194FBA").ss(1,0,0,4).p("ABVAQQgPgUgTgSQgugqgfAXQgKAJgMAQQgZAfgJAiQgDABgEAA");
	this.shape_2.setTransform(87.8376,4.9871);

	this.shape_3 = new cjs.Shape();
	this.shape_3.graphics.f().s("#194FBA").ss(1,0,0,4).p("ACjgQQgPgWgVgTQgugpggAXQgKAJgMAQQgZAggIAhQgfAEglAOQg7AVgfAg");
	this.shape_3.setTransform(80.2048,8.4356);

	this.shape_4 = new cjs.Shape();
	this.shape_4.graphics.f().s("#194FBA").ss(1,0,0,4).p("AD7htQgGgBgDACQgQgZgXgVQgtgpggAXQgLAJgMAQQgYAggJAiQgfAEglAOQhJAagdAqQgfAHgiAdQg9A0gUBc");
	this.shape_4.setTransform(72.5577,18.1198);

	this.shape_5 = new cjs.Shape();
	this.shape_5.graphics.f().s("#194FBA").ss(1,0,0,4).p("AFEjrQgRgZgWgVQgugpggAXQgKAJgMAQQgZAggJAiQgfAEglAOQhJAagcArQgfAHgiAdQhFA7gSBtQgVAOggApQg1BEgvBq");
	this.shape_5.setTransform(64.4343,30.842);

	this.shape_6 = new cjs.Shape();
	this.shape_6.graphics.f().s("#194FBA").ss(1,0,0,4).p("AGhmMQgBAAgCAAQgQgZgWgUQgugqggAXQgLAJgLAQQgZAggJAiQgfAFglANQhJAageArQgfAHgiAeQhEA6gRBvQgVAOggApQhABRg4CJQhNBxhJCIQgGAMgGAL");
	this.shape_6.setTransform(55.35,46.8524);

	this.shape_7 = new cjs.Shape();
	this.shape_7.graphics.f().s("#194FBA").ss(1,0,0,4).p("AHoqdQgDAAgBABQgQgZgXgVQgtgpggAXQgLAJgMAQQgYAggJAiQgfAEglAOQhKAagdArQgfAHgiAdQhFA7gQBuQgVAOggApQhABSg4CJQhNBwhJCJQiTERASB1IgJAoQgKAygCAuQgCAYABAW");
	this.shape_7.setTransform(48.3932,74.1374);

	this.shape_8 = new cjs.Shape();
	this.shape_8.graphics.f().s("#194FBA").ss(1,0,0,4).p("AHouaQgDAAgBABQgQgZgXgVQgtgpggAXQgLAJgMAQQgYAggJAiQgfAEglAOQhKAagdArQgfAHgiAdQhFA7gQBuQgVAOggApQhABSg4CJQhNBxhJCJQiTEQASB1IgJAoQgKAygCAuQgHCWBGBCIARArQAYA0AbAwQBWCUBeAp");
	this.shape_8.setTransform(48.3932,99.4318);

	this.shape_9 = new cjs.Shape();
	this.shape_9.graphics.f().s("#194FBA").ss(1,0,0,4).p("AHkxMQgRgYgVgUQgugqggAXQgKAJgMAQQgZAggJAiQgfAFglANQhJAagdArQgfAHgiAeQhFA6gRBvQgVAOggApQhABSg3CJQhNBxhJCIQiTEQARB1IgJAoQgKAygCAvQgHCWBGBBIASArQAXA1AbAvQBYCXBgAoIAcAXQAiAdAcAfQBZBhgDBHIATA3QAXA2AXgJIACgI");
	this.shape_9.setTransform(48.3432,117.1542);

	this.shape_10 = new cjs.Shape();
	this.shape_10.graphics.f().s("#194FBA").ss(1,0,0,4).p("AHjxVQgQgYgVgUQgugpggAXQgKAJgMAQQgZAggJAiQgfAEglAOQhJAagdArQgfAHgiAdQhFA7gRBuQgVAOggApQhABSg3CJQhNBxhJCJQiTEQARB1IgJAoQgKAygCAuQgHCWBGBCIASArQAXA0AbAwQBYCWBgAoIAcAXQAiAeAcAeQBZBhgDBHIATA4QAXA2AXgJIAKguIAPApQASAlASgXIgBhTQAOgSAHgb");
	this.shape_10.setTransform(48.3182,118.0058);

	this.shape_11 = new cjs.Shape();
	this.shape_11.graphics.f().s("#194FBA").ss(1,0,0,4).p("AHnxUQgCAAgBABQgQgZgXgVQgtgpggAXQgLAJgMAQQgYAggJAiQgfAEglAOQhKAagdArQgfAHgiAdQhFA7gQBuQgVAOggApQhABSg4CJQhNBxhJCJQiTEQASB1IgJAoQgKAygCAuQgHCWBGBCIARArQAYA0AbAwQBXCWBgAoIAdAXQAiAeAcAeQBZBhgDBHIASA4QAYA2AXgJIAJguIAPApQATAlARgXIgBhTQATgYAGgnQgIgyAChHQAEh4Amhd");
	this.shape_11.setTransform(48.3932,118.0058);

	this.shape_12 = new cjs.Shape();
	this.shape_12.graphics.f().s("#194FBA").ss(1,0,0,4).p("AHnxUQgCAAgBABQgQgZgXgVQgtgpggAXQgLAJgMAQQgYAggJAiQgfAEglAOQhKAagdArQgfAHgiAdQhFA7gQBuQgVAOggApQhABSg4CJQhNBxhJCJQiTEQASB1IgJAoQgKAygCAuQgHCWBGBCIARArQAYA0AbAwQBXCWBgAoIAdAXQAiAeAcAeQBZBhgDBHIASA4QAYA2AXgJIAJguIAPApQATAlARgXIgBhTQATgYAGgnQgIgyAChHQAFiOA0hnQBFhABBhgQAlg2AZg0");
	this.shape_12.setTransform(48.3932,118.0058);

	this.shape_13 = new cjs.Shape();
	this.shape_13.graphics.f().s("#194FBA").ss(1,0,0,4).p("AHZxXQgPgXgVgTQgugpggAXQgKAJgMAQQgZAggJAiQgfAEglAOQhJAagdArQgfAHgiAdQhEA7gSBuQgVAOggApQhABSg3CJQhNBxhJCJQiTEQARB1IgJAoQgKAygCAuQgHCWBGBCIASArQAXA0AbAwQBYCWBgAoIAcAXQAiAeAcAeQBZBhgDBHIATA4QAXA2AXgJIAKguIAPApQASAlASgXIgBhTQASgYAGgnQgIgyADhHQAEiOA1hnQBFhABBhgQCCjBgRijQAPiDATiJQAFgoAFgj");
	this.shape_13.setTransform(49.2182,118.0058);

	this.shape_14 = new cjs.Shape();
	this.shape_14.graphics.f().s("#194FBA").ss(1,0,0,4).p("AGaxUQgQgYgWgVQgtgpggAXQgLAJgMAQQgYAggJAiQgfAEglAOQhKAagdArQgfAHgiAdQhEA7gRBuQgVAOggApQhABSg4CJQhNBxhJCJQiTEQASB1IgJAoQgKAygCAuQgHCWBGBCIARArQAYA0AbAwQBXCWBgAoIAdAXQAiAeAcAeQBaBhgDBHIASA4QAYA2AWgJIAJguIAPApQATAlARgXIgBhTQATgYAGgnQgIgyAChHQAFiOA0hnQBFhABBhgQCDjBgSijQAPiDATiJQAlkTASgfQAWglAYgyQAZg0AOgq");
	this.shape_14.setTransform(55.6932,118.0058);

	this.shape_15 = new cjs.Shape();
	this.shape_15.graphics.f().s("#194FBA").ss(1,0,0,4).p("AGSxUQgQgYgWgVQgugpggAXQgKAJgMAQQgZAggJAiQgfAEglAOQhJAagdArQgfAHghAdQhFA7gSBuQgVAOggApQhABSg3CJQhNBxhJCJQiTEQARB1IgJAoQgKAygCAuQgHCWBGBCIASArQAXA0AbAwQBYCWBgAoIAcAXQAiAeAcAeQBaBhgDBHIATA4QAXA2AXgJIAJguIAPApQASAlASgXIgBhTQASgYAGgnQgIgyADhHQAEiOA1hnQBFhABBhgQCCjBgRijQAPiDATiJQAlkTARgfQAWglAYgyQAwhjAJhCQgKgtgSg8QAAgZAHgiQABgDABgE");
	this.shape_15.setTransform(56.5465,118.0058);

	this.shape_16 = new cjs.Shape();
	this.shape_16.graphics.f().s("#194FBA").ss(1,0,0,4).p("AJCvqQgJgugdAAQgjgRglgQQhJgggJAGQgQgZgXgVQgtgpggAXQgLAJgMAQQgYAggJAiQgfAEglAOQhKAagdArQgfAHghAdQhFA7gRBuQgVAOggApQhABSg4CJQhNBxhJCJQiTEQASB1IgJAoQgKAygCAuQgHCWBGBCIARArQAYA0AbAwQBXCWBgAoIAdAXQAiAeAcAeQBaBhgDBHIASA4QAYA2AXgJIAJguIAOApQATAlARgXIgBhTQATgYAGgnQgIgyAChHQAFiOA0hnQBFhABBhgQCDjBgSijQAPiDATiJQAlkTASgfQAWglAYgyQAwhjAIhCQgJgtgTg8QAAgZAIgiQAPhDAmgrQABgXgFgWg");
	this.shape_16.setTransform(58.1773,118.0058);

	this.timeline.addTween(cjs.Tween.get({}).to({state:[{t:this.shape_1}]}).to({state:[{t:this.shape_2}]},1).to({state:[{t:this.shape_3}]},1).to({state:[{t:this.shape_4}]},1).to({state:[{t:this.shape_5}]},1).to({state:[{t:this.shape_6}]},1).to({state:[{t:this.shape_7}]},1).to({state:[{t:this.shape_8}]},1).to({state:[{t:this.shape_9}]},1).to({state:[{t:this.shape_10}]},1).to({state:[{t:this.shape_11}]},1).to({state:[{t:this.shape_12}]},1).to({state:[{t:this.shape_13}]},1).to({state:[{t:this.shape_14}]},1).to({state:[{t:this.shape_15}]},1).to({state:[{t:this.shape_16}]},1).wait(50));

	// 圖層_1
	this.shape_17 = new cjs.Shape();
	this.shape_17.graphics.f().s("#194FBA").ss(1,0,0,4).p("AHUw5QhJgggJAGQgQgZgXgVQgtgpggAXQgLAJgMAQQgYAggJAiQgfAEglAOQhKAagdArQgfAHghAdQhFA7gRBuQgVAOggApQhABSg4CJQhNBxhJCJQiTEQASB1IgJAoQgKAygCAuQgHCWBGBCIARArQAYA0AbAwQBXCWBgAoIAdAXQAiAeAcAeQBaBhgDBHIASA4QAYA2AXgJIAJguIAOApQATAlARgXIgBhTQATgYAGgnQgIgyAChHQAFiOA0hnQBFhABBhgQCDjBgSijQAPiDATiJQAlkTASgfQAWglAYgyQAwhjAIhCQgJgtgTg8QAAgZAIgiQAPhDAmgrQABgXgFgWQgJgugdAAQgjgRglgQg");
	this.shape_17.setTransform(58.1773,118.0058);

	this.shape_18 = new cjs.Shape();
	this.shape_18.graphics.f("#FFFFFF").s().p("AAASFIgOgpIgJAuQgXAJgYg2IgSg4QADhHhahhQgcgegigeIgdgXQhggohXiWQgbgwgYg0IgRgrQhGhCAHiWQACguAKgyIAJgoQgSh1CTkQQBJiJBNhxQA4iJBAhSQAggpAVgOQARhuBFg7QAhgdAfgHQAdgrBKgaQAlgOAfgEQAJgiAYggQAMgQALgJQAggXAtApQAXAVAQAZQAJgGBJAgQAlAQAjARQAdAAAJAuQAFAWgBAXQgmArgPBDQgIAiAAAZQATA8AJAtQgIBCgwBjQgYAygWAlQgSAfglETQgTCJgPCDQASCjiDDBQhBBghFBAQg0BngFCOQgCBHAIAyQgGAngTAYIABBTQgGAJgHAAQgLAAgMgXg");
	this.shape_18.setTransform(58.1773,118.0058);

	this.timeline.addTween(cjs.Tween.get({}).to({state:[]}).to({state:[{t:this.shape_18},{t:this.shape_17}]},17).wait(48));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-1,-1,118.4,238.1);


(lib.數動態 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// timeline functions:
	this.frame_7 = function() {
		/* stop();*/
	}

	// actions tween:
	this.timeline.addTween(cjs.Tween.get(this).wait(7).call(this.frame_7).wait(1));

	// 圖層_1
	this.instance = new lib.數();
	this.instance.setTransform(14,65.2,1,0.4655,0,0,0,14,65.2);

	this.timeline.addTween(cjs.Tween.get(this.instance).to({scaleY:1.3481},4).to({scaleY:1},3).wait(1));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-1,-23.3,28.4,88.7);


(lib.海水動態 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// 圖層_1
	this.instance = new lib.海水();
	this.instance.setTransform(99.2,18.1,1,1,0,0,0,99.2,14.1);

	this.timeline.addTween(cjs.Tween.get(this.instance).to({x:101.2,y:14.1},10).to({x:105.7,y:17.6},9).to({x:99.2,y:18.1},10).wait(1));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-0.8,-0.6,206.4,47.300000000000004);


(lib.全1 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// 圖層_1
	this.instance = new lib.CachedBmp_46();
	this.instance.setTransform(487.05,5.65,0.3014,0.3014);

	this.instance_1 = new lib.片段群組0_1();
	this.instance_1.setTransform(808.5,183.2,1,1,0,0,0,37.4,44.6);

	this.instance_2 = new lib.CachedBmp_45();
	this.instance_2.setTransform(735,98.3,0.3014,0.3014);

	this.instance_3 = new lib.CachedBmp_44();
	this.instance_3.setTransform(-0.8,-0.5,0.3014,0.3014);

	this.timeline.addTween(cjs.Tween.get({}).to({state:[{t:this.instance_3},{t:this.instance_2},{t:this.instance_1},{t:this.instance}]}).wait(1));

	this._renderFirstFrame();

}).prototype = getMCSymbolPrototype(lib.全1, new cjs.Rectangle(-0.8,-0.5,1106.5,244.8), null);


(lib.船身背氣瓶 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// 圖層_1
	this.instance = new lib.船身();
	this.instance.setTransform(106.7,76.3,1,1,0,0,0,106.7,76.3);

	this.instance_1 = new lib.CachedBmp_40();
	this.instance_1.setTransform(8.7,90.55,0.2684,0.2684);

	this.instance_2 = new lib.氣瓶();
	this.instance_2.setTransform(85.3,92.7,1,1,0,0,0,11.6,25);

	this.instance_3 = new lib.氣瓶();
	this.instance_3.setTransform(70.85,96.75,1,1,0,0,0,11.6,25);

	this.instance_4 = new lib.氣瓶();
	this.instance_4.setTransform(56.2,100.7,1,1,0,0,0,11.6,25);

	this.instance_5 = new lib.氣瓶();
	this.instance_5.setTransform(41.45,104.25,1,1,0,0,0,11.6,25);

	this.instance_6 = new lib.氣瓶();
	this.instance_6.setTransform(27.05,108.75,1,1,0,0,0,11.6,25);

	this.instance_7 = new lib.氣瓶();
	this.instance_7.setTransform(12.55,112.9,1,1,0,0,0,11.6,25);

	this.instance_8 = new lib.氣瓶暗();
	this.instance_8.setTransform(89.75,84.5,1,1,0,0,0,11.7,25);

	this.instance_9 = new lib.氣瓶暗();
	this.instance_9.setTransform(76.2,89,1,1,0,0,0,11.7,25);

	this.instance_10 = new lib.氣瓶暗();
	this.instance_10.setTransform(62.45,93.5,1,1,0,0,0,11.7,25);

	this.instance_11 = new lib.氣瓶暗();
	this.instance_11.setTransform(48.8,97.6,1,1,0,0,0,11.7,25);

	this.instance_12 = new lib.氣瓶暗();
	this.instance_12.setTransform(34.8,101.85,1,1,0,0,0,11.7,25);

	this.instance_13 = new lib.氣瓶暗();
	this.instance_13.setTransform(21.1,106,1,1,0,0,0,11.7,25);

	this.timeline.addTween(cjs.Tween.get({}).to({state:[{t:this.instance_13},{t:this.instance_12},{t:this.instance_11},{t:this.instance_10},{t:this.instance_9},{t:this.instance_8},{t:this.instance_7},{t:this.instance_6},{t:this.instance_5},{t:this.instance_4},{t:this.instance_3},{t:this.instance_2},{t:this.instance_1},{t:this.instance}]}).wait(1));

	this._renderFirstFrame();

}).prototype = getMCSymbolPrototype(lib.船身背氣瓶, new cjs.Rectangle(-0.8,-0.5,215.3,153.8), null);


(lib.氣瓶抖 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// 圖層_1
	this.instance = new lib.氣瓶01();
	this.instance.setTransform(11.8,42.6,1,1,0,0,0,11.8,42.6);

	this.timeline.addTween(cjs.Tween.get(this.instance).wait(1).to({y:43.15},0).wait(1));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-0.5,-0.3,24.4,86.5);


(lib.氣瓶全 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// timeline functions:
	this.frame_16 = function() {
		/* stop();*/
	}

	// actions tween:
	this.timeline.addTween(cjs.Tween.get(this).wait(16).call(this.frame_16).wait(1));

	// 圖層_6
	this.instance = new lib.氣瓶01();
	this.instance.setTransform(11.2,42.95,1,1,0,0,0,11.8,42.6);
	this.instance._off = true;

	this.instance_1 = new lib.氣瓶抖();
	this.instance_1.setTransform(37.1,42.95,1,1,0,0,0,11.8,42.6);

	this.timeline.addTween(cjs.Tween.get({}).to({state:[]}).to({state:[{t:this.instance}]},9).to({state:[{t:this.instance}]},3).to({state:[{t:this.instance}]},2).to({state:[{t:this.instance_1}]},2).wait(1));
	this.timeline.addTween(cjs.Tween.get(this.instance).wait(9).to({_off:false},0).to({x:22.2},3).to({x:11.2},2).to({_off:true},2).wait(1));

	// 圖層_5
	this.instance_2 = new lib.氣瓶01();
	this.instance_2.setTransform(37.1,42.6,1,1,0,0,0,11.8,42.6);
	this.instance_2._off = true;

	this.instance_3 = new lib.氣瓶抖();
	this.instance_3.setTransform(11.2,42.95,1,1,0,0,0,11.8,42.6);

	this.timeline.addTween(cjs.Tween.get({}).to({state:[]}).to({state:[{t:this.instance_2}]},7).to({state:[{t:this.instance_2}]},3).to({state:[{t:this.instance_2}]},2).to({state:[{t:this.instance_3}]},4).wait(1));
	this.timeline.addTween(cjs.Tween.get(this.instance_2).wait(7).to({_off:false},0).to({x:46.45},3).to({x:37.1},2).to({_off:true},4).wait(1));

	// 圖層_4
	this.instance_4 = new lib.氣瓶01();
	this.instance_4.setTransform(62.4,42.6,1,1,0,0,0,11.8,42.6);
	this.instance_4._off = true;

	this.instance_5 = new lib.氣瓶抖();
	this.instance_5.setTransform(62.4,42.95,1,1,0,0,0,11.8,42.6);

	this.timeline.addTween(cjs.Tween.get({}).to({state:[]}).to({state:[{t:this.instance_4}]},5).to({state:[{t:this.instance_4}]},3).to({state:[{t:this.instance_4}]},2).to({state:[{t:this.instance_5}]},6).wait(1));
	this.timeline.addTween(cjs.Tween.get(this.instance_4).wait(5).to({_off:false},0).to({x:70.65},3).to({x:62.4},2).to({_off:true},6).wait(1));

	// 圖層_3
	this.instance_6 = new lib.氣瓶01();
	this.instance_6.setTransform(88.25,42.6,1,1,0,0,0,11.8,42.6);
	this.instance_6._off = true;

	this.instance_7 = new lib.氣瓶抖();
	this.instance_7.setTransform(88.3,42.95,1,1,0,0,0,11.8,42.6);

	this.timeline.addTween(cjs.Tween.get({}).to({state:[]}).to({state:[{t:this.instance_6}]},4).to({state:[{t:this.instance_6}]},3).to({state:[{t:this.instance_6}]},2).to({state:[{t:this.instance_7}]},7).wait(1));
	this.timeline.addTween(cjs.Tween.get(this.instance_6).wait(4).to({_off:false},0).to({x:94.85},3).to({x:88.25},2).to({_off:true},7).wait(1));

	// 圖層_2
	this.instance_8 = new lib.氣瓶01();
	this.instance_8.setTransform(113.55,42.6,1,1,0,0,0,11.8,42.6);
	this.instance_8._off = true;

	this.instance_9 = new lib.氣瓶抖();
	this.instance_9.setTransform(113.6,42.95,1,1,0,0,0,11.8,42.6);

	this.timeline.addTween(cjs.Tween.get({}).to({state:[]}).to({state:[{t:this.instance_8}]},2).to({state:[{t:this.instance_8}]},3).to({state:[{t:this.instance_8}]},2).to({state:[{t:this.instance_9}]},9).wait(1));
	this.timeline.addTween(cjs.Tween.get(this.instance_8).wait(2).to({_off:false},0).to({x:119.05},3).to({x:113.55},2).to({_off:true},9).wait(1));

	// 圖層_1
	this.instance_10 = new lib.氣瓶01();
	this.instance_10.setTransform(122.75,42.6,1,1,0,0,0,11.8,42.6);
	this.instance_10.alpha = 0;

	this.instance_11 = new lib.氣瓶抖();
	this.instance_11.setTransform(138.9,42.95,1,1,0,0,0,11.8,42.6);

	this.timeline.addTween(cjs.Tween.get({}).to({state:[{t:this.instance_10}]}).to({state:[{t:this.instance_10}]},3).to({state:[{t:this.instance_10}]},2).to({state:[{t:this.instance_11}]},11).wait(1));
	this.timeline.addTween(cjs.Tween.get(this.instance_10).to({x:142.7,alpha:1},3).to({x:138.85},2).to({_off:true},11).wait(1));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-1.1,-0.3,155.9,86.3);


(lib.紅綠燈動態 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// timeline functions:
	this.frame_8 = function() {
		/* stop();*/
	}

	// actions tween:
	this.timeline.addTween(cjs.Tween.get(this).wait(8).call(this.frame_8).wait(1));

	// 圖層_1
	this.instance = new lib.紅綠東();
	this.instance.setTransform(15.4,99.8,1,0.3697,0,0,0,15.4,99.8);

	this.timeline.addTween(cjs.Tween.get(this.instance).to({scaleY:1.3336,y:100.2},5).to({scaleY:1,y:99.8},3).wait(1));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(0,-32.9,30.9,131.8);


(lib.背氣瓶船身動態 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// 圖層_1
	this.instance = new lib.船身背氣瓶();
	this.instance.setTransform(106.7,76.3,1,1,0,0,0,106.7,76.3);

	this.timeline.addTween(cjs.Tween.get(this.instance).to({y:80.3},14).to({y:76.3},15).wait(1));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-0.8,-0.5,215.3,157.8);


(lib.船動畫 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// 圖層_2
	this.instance = new lib.海水動態();
	this.instance.setTransform(106.1,142.65,1,1,0,0,0,99.2,14.1);

	this.timeline.addTween(cjs.Tween.get(this.instance).wait(60));

	// 圖層_1
	this.instance_1 = new lib.背氣瓶船身動態();
	this.instance_1.setTransform(107.7,76.3,1,1,0,0,0,106.7,76.3);

	this.timeline.addTween(cjs.Tween.get(this.instance_1).wait(60));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(0.2,-0.5,215.3,175.8);


(lib.補間動畫2 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// 圖層_1
	this.instance = new lib.船動畫();
	this.instance.setTransform(-0.65,-12.45,1.3348,1.3348,0,0,0,107.4,78.3);

	this.timeline.addTween(cjs.Tween.get(this.instance).wait(1));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-143.7,-117.6,287.29999999999995,234.5);


(lib.全 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// timeline functions:
	this.frame_155 = function() {
		/* stop();*/
	}

	// actions tween:
	this.timeline.addTween(cjs.Tween.get(this).wait(155).call(this.frame_155).wait(1));

	// 台灣
	this.instance = new lib.台灣("synched",0,false);
	this.instance.setTransform(392.45,6.1,0.8917,0.8917,0,0,0,58.2,118);
	this.instance._off = true;

	this.timeline.addTween(cjs.Tween.get(this.instance).wait(70).to({_off:false},0).wait(11).to({startPosition:11},0).to({_off:true},1).wait(7).to({_off:false,startPosition:19},0).wait(42).to({startPosition:61},0).to({_off:true},1).wait(7).to({_off:false,startPosition:64},0).wait(17));

	// 工廠
	this.instance_1 = new lib.工廠();
	this.instance_1.setTransform(950.6,34.65,1.245,1.245,0,0,0,89.9,64.2);
	this.instance_1.alpha = 0;
	this.instance_1._off = true;

	this.timeline.addTween(cjs.Tween.get(this.instance_1).wait(127).to({_off:false},0).to({regY:64.1,scaleX:1.2449,scaleY:1.2449,x:910.4,y:34.6,alpha:0.3984},4).to({_off:true},1).wait(7).to({_off:false,regY:64.2,scaleX:1.245,scaleY:1.245,x:850.05,y:34.65,alpha:1},0).wait(17));

	// 貨車
	this.instance_2 = new lib.空貨車();
	this.instance_2.setTransform(613.2,53.8,1,1,0,0,180,121.2,39.3);
	this.instance_2._off = true;

	this.timeline.addTween(cjs.Tween.get(this.instance_2).wait(21).to({_off:false},0).to({x:54.6},17).wait(2).to({skewY:0,x:23.1},0).wait(11).to({_off:true},1).wait(8).to({_off:false},0).wait(21).to({_off:true},1).wait(7).to({_off:false,regX:121.3,regY:39.4,scaleX:0.6167,scaleY:0.6167,x:376.95,y:68.3},0).to({regX:121.2,regY:39.3,scaleX:0.5764,scaleY:0.5764,x:414.15,y:69.75},2).to({scaleX:0.4171,scaleY:0.4171,x:444.4,y:76.1,alpha:0},5).wait(31).to({regY:39.4,scaleX:0.5886,scaleY:0.5886,x:430.75,y:69.35},0).wait(4).to({_off:true},1).wait(7).to({_off:false,scaleX:0.7278,scaleY:0.7278,x:548.75,y:63.8,alpha:0.8555},0).to({regY:39.3,scaleX:0.751,scaleY:0.751,x:568.4,y:62.8,alpha:1},2).to({regY:39.2,scaleX:0.5653,scaleY:0.5653,x:790,y:70.1,alpha:0},14).wait(1));

	// 氣瓶
	this.instance_3 = new lib.氣瓶全("synched",0,false);
	this.instance_3.setTransform(-1.55,13.7,1,1,0,0,0,75.2,42.6);
	this.instance_3._off = true;

	this.timeline.addTween(cjs.Tween.get(this.instance_3).wait(49).to({_off:false},0).wait(2).to({startPosition:2},0).to({_off:true},1).wait(8).to({_off:false,startPosition:11},0).wait(10).to({startPosition:16},0).to({startPosition:16},11).to({_off:true},1).wait(7).to({_off:false,regY:42.8,scaleX:0.6167,scaleY:0.6167,x:361.7,y:43.65},0).to({regX:75.3,regY:42.7,scaleX:0.5764,scaleY:0.5764,x:400,y:46.7},2).to({regX:75.4,regY:42.6,scaleX:0.4171,scaleY:0.4171,x:434.2,y:59.35,alpha:0,startPosition:0},5).wait(31).to({regX:75.2,scaleX:0.5886,scaleY:0.5886,x:416.2,y:45.7,mode:"single",startPosition:16},0).to({startPosition:16},4).to({_off:true},1).wait(7).to({_off:false,regX:75.3,scaleX:0.7278,scaleY:0.7278,x:530.85,y:34.5,alpha:0.8555},0).to({regX:75.2,regY:42.7,scaleX:0.751,scaleY:0.751,x:549.9,y:32.7,alpha:1},2).to({regY:42.6,scaleX:0.5653,scaleY:0.5653,x:775.05,y:47.5,alpha:0,startPosition:15},14).wait(1));

	// 碼頭栓
	this.instance_4 = new lib.碼頭("synched",0,false);
	this.instance_4.setTransform(-288.2,120.3,1.2847,1.2847,0,0,0,14,16.1);
	this.instance_4._off = true;

	this.timeline.addTween(cjs.Tween.get(this.instance_4).wait(21).to({_off:false},0).wait(30).to({startPosition:5},0).to({_off:true},1).wait(8).to({_off:false},0).wait(21).to({startPosition:5},0).to({_off:true},1).wait(7).to({_off:false},0).wait(42).to({startPosition:5},0).to({_off:true},1).wait(7).to({_off:false},0).wait(17));

	// 遮色片 (mask)
	var mask = new cjs.Shape();
	mask._off = true;
	var mask_graphics_9 = new cjs.Graphics().p("EBWhAKZIAAuhIDRAAIAAOhg");
	var mask_graphics_10 = new cjs.Graphics().p("ArNHRIAAuhIWbAAIAAOhg");
	var mask_graphics_11 = new cjs.Graphics().p("A0yHRIAAuhMAplAAAIAAOhg");
	var mask_graphics_12 = new cjs.Graphics().p("A+XHRIAAuhMA8vAAAIAAOhg");
	var mask_graphics_13 = new cjs.Graphics().p("Egn8AHRIAAuhMBP5AAAIAAOhg");
	var mask_graphics_14 = new cjs.Graphics().p("EgxhAHRIAAuhMBjDAAAIAAOhg");
	var mask_graphics_15 = new cjs.Graphics().p("Eg7GAHRIAAuhMB2NAAAIAAOhg");
	var mask_graphics_16 = new cjs.Graphics().p("EhErAHRIAAuhMCJXAAAIAAOhg");
	var mask_graphics_17 = new cjs.Graphics().p("EhOQAHRIAAuhMCchAAAIAAOhg");
	var mask_graphics_18 = new cjs.Graphics().p("EhX1AHRIAAuhMCvrAAAIAAOhg");
	var mask_graphics_19 = new cjs.Graphics().p("EhhaAHRIAAuhMDC1AAAIAAOhg");
	var mask_graphics_20 = new cjs.Graphics().p("Ehq/AHRIAAuhMDV/AAAIAAOhg");
	var mask_graphics_21 = new cjs.Graphics().p("Eh0kAKSIAAuhMDpJAAAIAAOhg");
	var mask_graphics_51 = new cjs.Graphics().p("Eh0kAHRIAAuhMDpJAAAIAAOhg");
	var mask_graphics_60 = new cjs.Graphics().p("Eh0kAHRIAAuhMDpJAAAIAAOhg");
	var mask_graphics_81 = new cjs.Graphics().p("Eh0kAHRIAAuhMDpJAAAIAAOhg");
	var mask_graphics_89 = new cjs.Graphics().p("Eh0kAHRIAAuhMDpJAAAIAAOhg");
	var mask_graphics_131 = new cjs.Graphics().p("Eh0kAHRIAAuhMDpJAAAIAAOhg");
	var mask_graphics_139 = new cjs.Graphics().p("Eh0kAHRIAAuhMDpJAAAIAAOhg");

	this.timeline.addTween(cjs.Tween.get(mask).to({graphics:null,x:0,y:0}).wait(9).to({graphics:mask_graphics_9,x:574.65,y:66.525}).wait(1).to({graphics:mask_graphics_10,x:1078.6,y:86.45}).wait(1).to({graphics:mask_graphics_11,x:1018.35,y:86.3}).wait(1).to({graphics:mask_graphics_12,x:958.15,y:86.2}).wait(1).to({graphics:mask_graphics_13,x:897.925,y:86.05}).wait(1).to({graphics:mask_graphics_14,x:837.7,y:85.9}).wait(1).to({graphics:mask_graphics_15,x:777.5,y:85.8}).wait(1).to({graphics:mask_graphics_16,x:717.275,y:85.7}).wait(1).to({graphics:mask_graphics_17,x:657.05,y:85.55}).wait(1).to({graphics:mask_graphics_18,x:596.825,y:85.45}).wait(1).to({graphics:mask_graphics_19,x:536.625,y:85.3}).wait(1).to({graphics:mask_graphics_20,x:476.375,y:85.15}).wait(1).to({graphics:mask_graphics_21,x:416.1525,y:65.775}).wait(30).to({graphics:mask_graphics_51,x:416.175,y:85.05}).wait(1).to({graphics:null,x:0,y:0}).wait(8).to({graphics:mask_graphics_60,x:416.175,y:85.05}).wait(21).to({graphics:mask_graphics_81,x:416.175,y:85.05}).wait(1).to({graphics:null,x:0,y:0}).wait(7).to({graphics:mask_graphics_89,x:416.175,y:85.05}).wait(42).to({graphics:mask_graphics_131,x:416.175,y:85.05}).wait(1).to({graphics:null,x:0,y:0}).wait(7).to({graphics:mask_graphics_139,x:416.175,y:85.05}).wait(17));

	// 道路
	this.shape = new cjs.Shape();
	this.shape.graphics.f().s("#194FBA").ss(1,0,0,4).p("Eh6OAFTIAABNEB6UgGaMjbwAAAI4jGU");
	this.shape.setTransform(474.6,133.775);

	this.shape_1 = new cjs.Shape();
	this.shape_1.graphics.f("#FFFFFF").s().p("EBnzAGGIAAABMjiEgAgIAAlbIAQAAIYjmSMDbvAAAIAAMNg");
	this.shape_1.setTransform(474.85,131.825);

	var maskedShapeInstanceList = [this.shape,this.shape_1];

	for(var shapedInstanceItr = 0; shapedInstanceItr < maskedShapeInstanceList.length; shapedInstanceItr++) {
		maskedShapeInstanceList[shapedInstanceItr].mask = mask;
	}

	this.timeline.addTween(cjs.Tween.get({}).to({state:[]}).to({state:[{t:this.shape_1},{t:this.shape}]},9).to({state:[{t:this.shape_1},{t:this.shape}]},42).to({state:[]},1).to({state:[{t:this.shape_1},{t:this.shape}]},8).to({state:[{t:this.shape_1},{t:this.shape}]},21).to({state:[]},1).to({state:[{t:this.shape_1},{t:this.shape}]},7).to({state:[{t:this.shape_1},{t:this.shape}]},42).to({state:[]},1).to({state:[{t:this.shape_1},{t:this.shape}]},7).wait(17));

	// 碼頭栓
	this.instance_5 = new lib.碼頭("synched",0,false);
	this.instance_5.setTransform(-135.05,87.1,1,1,0,0,0,14,16.1);
	this.instance_5._off = true;

	this.timeline.addTween(cjs.Tween.get(this.instance_5).wait(26).to({_off:false},0).wait(25).to({startPosition:5},0).to({_off:true},1).wait(8).to({_off:false},0).wait(21).to({startPosition:5},0).to({_off:true},1).wait(7).to({_off:false},0).wait(42).to({startPosition:5},0).to({_off:true},1).wait(7).to({_off:false},0).wait(17));

	// 圖層_15
	this.instance_6 = new lib.數動態("synched",0,false);
	this.instance_6.setTransform(278.75,71.2,0.6714,0.6714,0,0,0,13.2,31.9);
	this.instance_6._off = true;

	this.timeline.addTween(cjs.Tween.get(this.instance_6).wait(19).to({_off:false},0).wait(32).to({startPosition:7},0).to({_off:true},1).wait(8).to({_off:false},0).wait(21).to({startPosition:7},0).to({_off:true},1).wait(7).to({_off:false},0).wait(42).to({startPosition:7},0).to({_off:true},1).wait(7).to({_off:false},0).wait(17));

	// 圖層_14
	this.instance_7 = new lib.數動態("synched",0,false);
	this.instance_7.setTransform(35.1,60,1,1,0,0,0,13.2,31.9);

	this.instance_8 = new lib.數動態("synched",0,false);
	this.instance_8.setTransform(611.3,60.05,1,1,0,0,0,13.2,31.9);

	this.timeline.addTween(cjs.Tween.get({}).to({state:[]}).to({state:[{t:this.instance_7,p:{startPosition:0,mode:"synched",loop:false}}]},28).to({state:[{t:this.instance_7,p:{startPosition:7,mode:"synched",loop:false}}]},23).to({state:[]},1).to({state:[{t:this.instance_7,p:{startPosition:7,mode:"synched",loop:false}}]},8).to({state:[{t:this.instance_7,p:{startPosition:7,mode:"synched",loop:false}}]},21).to({state:[]},1).to({state:[{t:this.instance_8,p:{startPosition:0}},{t:this.instance_7,p:{startPosition:4,mode:"single",loop:undefined}}]},7).to({state:[{t:this.instance_8,p:{startPosition:7}},{t:this.instance_7,p:{startPosition:4,mode:"single",loop:undefined}}]},42).to({state:[]},1).to({state:[{t:this.instance_8,p:{startPosition:7}},{t:this.instance_7,p:{startPosition:4,mode:"single",loop:undefined}}]},7).wait(17));

	// 樹木
	this.instance_9 = new lib.數動態("synched",0,false);
	this.instance_9.setTransform(-20.2,70.75,0.6714,0.6714,0,0,0,13.2,31.9);

	this.instance_10 = new lib.數動態("synched",6,false);
	this.instance_10.setTransform(563.55,71.75,0.6714,0.6714,0,0,0,13.2,31.9);

	this.timeline.addTween(cjs.Tween.get({}).to({state:[]}).to({state:[{t:this.instance_9,p:{startPosition:0}}]},27).to({state:[{t:this.instance_9,p:{startPosition:7}}]},24).to({state:[]},1).to({state:[{t:this.instance_9,p:{startPosition:7}}]},8).to({state:[{t:this.instance_9,p:{startPosition:7}}]},21).to({state:[]},1).to({state:[{t:this.instance_10,p:{startPosition:6}},{t:this.instance_9,p:{startPosition:6}}]},7).to({state:[{t:this.instance_10,p:{startPosition:7}},{t:this.instance_9,p:{startPosition:7}}]},42).to({state:[]},1).to({state:[{t:this.instance_10,p:{startPosition:7}},{t:this.instance_9,p:{startPosition:7}}]},7).wait(17));

	// 紅綠燈
	this.instance_11 = new lib.紅綠燈動態("synched",0,false);
	this.instance_11.setTransform(164.3,44.9,1,1,0,0,0,15.4,48.6);
	this.instance_11._off = true;

	this.timeline.addTween(cjs.Tween.get(this.instance_11).wait(21).to({_off:false},0).wait(30).to({startPosition:8},0).to({_off:true},1).wait(8).to({_off:false},0).wait(21).to({startPosition:8},0).to({_off:true},1).wait(7).to({_off:false},0).wait(42).to({startPosition:8},0).to({_off:true},1).wait(7).to({_off:false},0).wait(17));

	// 船
	this.instance_12 = new lib.補間動畫2("synched",0);
	this.instance_12.setTransform(-475.7,-0.3);

	this.timeline.addTween(cjs.Tween.get(this.instance_12).to({x:-290.7,y:1.7},15).wait(36).to({startPosition:0},0).to({_off:true},1).wait(8).to({_off:false},0).wait(21).to({startPosition:0},0).to({_off:true},1).wait(7).to({_off:false},0).wait(42).to({startPosition:0},0).to({_off:true},1).wait(7).to({_off:false},0).wait(17));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-619.4,-117.9,1781.6999999999998,302.1);


// stage content:
(lib.KV = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	this.actionFrames = [176];
	// timeline functions:
	this.frame_176 = function() {
		/* stop();*/
	}

	// actions tween:
	this.timeline.addTween(cjs.Tween.get(this).wait(176).call(this.frame_176).wait(1));

	// 圖層_10
	this.instance = new lib.全1();
	this.instance.setTransform(-88.5,107.75,1.6588,1.6588,0,0,-0.0748,460.1,121.9);
	this.instance._off = true;

	this.timeline.addTween(cjs.Tween.get(this.instance).wait(159).to({_off:false},0).to({scaleX:1,scaleY:1,skewY:0,x:465.1,y:129.15},17).wait(1));

	// 圖層_8
	this.instance_1 = new lib.全("synched",0,false);
	this.instance_1.setTransform(494.7,132.55,0.9229,0.9229,0,0,0,95,1.4);

	this.timeline.addTween(cjs.Tween.get(this.instance_1).wait(52).to({startPosition:60},0).to({regY:1.3,scaleX:1.3957,scaleY:1.3957,x:596.7,y:119.5,startPosition:67},10).wait(15).to({x:594.7,startPosition:89},0).to({regY:1.4,scaleX:1.2944,scaleY:1.2944,x:32.55,y:131.55,startPosition:99},13).wait(33).to({startPosition:139},0).to({x:-460.4,startPosition:152},16).to({_off:true},20).wait(18));

	this._renderFirstFrame();

}).prototype = p = new lib.AnMovieClip();
p.nominalBounds = new cjs.Rectangle(-678.5,34.8,2764.9,280.4);
// library properties:
lib.properties = {
	id: 'AEF105D601581548B46500B56081319F',
	width: 934,
	height: 263,
	fps: 24,
	color: "#FFFFFF",
	opacity: 1.00,
	manifest: [
		{src:"images/CachedBmp_44.png?1635301169765", id:"CachedBmp_44"},
		{src:"images/KV_atlas_1.png?1635301169697", id:"KV_atlas_1"}
	],
	preloads: []
};



// bootstrap callback support:

(lib.Stage = function(canvas) {
	createjs.Stage.call(this, canvas);
}).prototype = p = new createjs.Stage();

p.setAutoPlay = function(autoPlay) {
	this.tickEnabled = autoPlay;
}
p.play = function() { this.tickEnabled = true; this.getChildAt(0).gotoAndPlay(this.getTimelinePosition()) }
p.stop = function(ms) { if(ms) this.seek(ms); this.tickEnabled = false; }
p.seek = function(ms) { this.tickEnabled = true; this.getChildAt(0).gotoAndStop(lib.properties.fps * ms / 1000); }
p.getDuration = function() { return this.getChildAt(0).totalFrames / lib.properties.fps * 1000; }

p.getTimelinePosition = function() { return this.getChildAt(0).currentFrame / lib.properties.fps * 1000; }

an.bootcompsLoaded = an.bootcompsLoaded || [];
if(!an.bootstrapListeners) {
	an.bootstrapListeners=[];
}

an.bootstrapCallback=function(fnCallback) {
	an.bootstrapListeners.push(fnCallback);
	if(an.bootcompsLoaded.length > 0) {
		for(var i=0; i<an.bootcompsLoaded.length; ++i) {
			fnCallback(an.bootcompsLoaded[i]);
		}
	}
};

an.compositions = an.compositions || {};
an.compositions['AEF105D601581548B46500B56081319F'] = {
	getStage: function() { return exportRoot.stage; },
	getLibrary: function() { return lib; },
	getSpriteSheet: function() { return ss; },
	getImages: function() { return img; }
};

an.compositionLoaded = function(id) {
	an.bootcompsLoaded.push(id);
	for(var j=0; j<an.bootstrapListeners.length; j++) {
		an.bootstrapListeners[j](id);
	}
}

an.getComposition = function(id) {
	return an.compositions[id];
}


an.makeResponsive = function(isResp, respDim, isScale, scaleType, domContainers) {		
	var lastW, lastH, lastS=1;		
	window.addEventListener('resize', resizeCanvas);		
	resizeCanvas();		
	function resizeCanvas() {			
		var w = lib.properties.width, h = lib.properties.height;			
		var iw = window.innerWidth, ih=window.innerHeight;			
		var pRatio = window.devicePixelRatio || 1, xRatio=iw/w, yRatio=ih/h, sRatio=1;			
		if(isResp) {                
			if((respDim=='width'&&lastW==iw) || (respDim=='height'&&lastH==ih)) {                    
				sRatio = lastS;                
			}				
			else if(!isScale) {					
				if(iw<w || ih<h)						
					sRatio = Math.min(xRatio, yRatio);				
			}				
			else if(scaleType==1) {					
				sRatio = Math.min(xRatio, yRatio);				
			}				
			else if(scaleType==2) {					
				sRatio = Math.max(xRatio, yRatio);				
			}			
		}
		domContainers[0].width = w * pRatio * sRatio;			
		domContainers[0].height = h * pRatio * sRatio;
		domContainers.forEach(function(container) {				
			container.style.width = w * sRatio + 'px';				
			container.style.height = h * sRatio + 'px';			
		});
		stage.scaleX = pRatio*sRatio;			
		stage.scaleY = pRatio*sRatio;
		lastW = iw; lastH = ih; lastS = sRatio;            
		stage.tickOnUpdate = false;            
		stage.update();            
		stage.tickOnUpdate = true;		
	}
}
an.handleSoundStreamOnTick = function(event) {
	if(!event.paused){
		var stageChild = stage.getChildAt(0);
		if(!stageChild.paused || stageChild.ignorePause){
			stageChild.syncStreamSounds();
		}
	}
}
an.handleFilterCache = function(event) {
	if(!event.paused){
		var target = event.target;
		if(target){
			if(target.filterCacheList){
				for(var index = 0; index < target.filterCacheList.length ; index++){
					var cacheInst = target.filterCacheList[index];
					if((cacheInst.startFrame <= target.currentFrame) && (target.currentFrame <= cacheInst.endFrame)){
						cacheInst.instance.cache(cacheInst.x, cacheInst.y, cacheInst.w, cacheInst.h);
					}
				}
			}
		}
	}
}


})(createjs = createjs||{}, AdobeAn = AdobeAn||{});
var createjs, AdobeAn;