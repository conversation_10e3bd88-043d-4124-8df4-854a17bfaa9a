<?php
//ini_set('display_errors','on');
ini_set('display_errors','off');
require_once($_SERVER["DOCUMENT_ROOT"]."/resonac-all/sysconfig/config.php");
require_once(LIBRARY_DIR."library/classes/admin/Control.php");
require_once(LIBRARY_DIR."library/classes/admin/Account.php");
require_once(LIBRARY_DIR."library/classes/admin/Language.php");
Authenticator::isAccountLogin("ss_account");

$lang_id = substr(trim($_REQUEST["lang_id"] ?? '1'),0,5);
$oLanguage = new Language();
$lang_id = $oLanguage->getLang($lang_id);
$LanguageAry = $oLanguage->Load_Language($lang_id, 'array');

require_once("common.php");

$oDB = new DB(DB_HOST, DB_NAME, DB_USER, DB_PASSWORD, true);
if (!$oDB->open()) {
  die(CONNECTION_DB_FAILED.$oDB->error());
}

$page = intval(trim($_REQUEST["page"] ?? '1')) ?: 1;
$pageSize = intval(trim($_REQUEST["pageSize"] ?? '20')) ?: 20; // 支援動態頁面大小
$tab = trim($_REQUEST["tab"] ?? 'current'); // current 或 history
$search_keyword = trim($_REQUEST["search_keyword"] ?? ''); // 搜尋關鍵字
$search_date_from = trim($_REQUEST["search_date_from"] ?? ''); // 開始日期
$search_date_to = trim($_REQUEST["search_date_to"] ?? ''); // 結束日期

// 確保session已啟動
if (!isset($_SESSION)) {
    session_start();
}

// 每次登入時清理超過一年的檔案
if (!isset($_SESSION['cleaned_old_files'])) {
    $cleanResult = cleanOldFiles();
    $_SESSION['cleaned_old_files'] = true;
    // 可以選擇是否顯示清理結果訊息
}

// 從資料庫取得資料
$convertResult = getConvertList($oDB, $tab, $search_keyword, $page, $pageSize, $search_date_from, $search_date_to);
$currentData = $convertResult['data'];
$totalRecords = $convertResult['total'];

// 取得Current和History的總筆數（用於標籤顯示）
$counts = getConvertCounts($oDB, $search_keyword, $search_date_from, $search_date_to);
$currentTotal = $counts['current'];
$historyTotal = $counts['history'];

$totalPages = ceil($totalRecords / $pageSize);
$page = ($page < 1) ? 1 : $page;
$page = ($page > $totalPages && $totalPages > 0) ? $totalPages : $page;

$show_color = ($lang_id=='1')?'green':'gray';
?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>台灣力森諾科特殊氣體股份有限公司-轉檔工具</title>
<style type="text/css">
  body {
    margin-left: 0px;
    margin-top: 0px;
    margin-right: 0px;
    margin-bottom: 0px;
    background-image: url();
  }
  .style1 {
    font-size: 12px;
    font-family: Verdana, Arial, Helvetica, sans-serif;
    font-weight: normal;
    color: #000000;
  }
  .style2 {
    color: #FFFFFF;
    font-family: Verdana, Arial, Helvetica, sans-serif;
    font-size: 12px;
    font-weight: normal;
  }
  .style2 a:link {
    color: #FFFFFF;
  }
  .style2 a:visited {
    color: #FFFFFF;
  }
  .style2 a:hover {
    color: #FD3904;
  }
  .style2 a:active {
    color: #FD3904;
  }
  .style1 a:link {
    color: #006600;
  }
  .style1 a:visited {
    color: #FD3904;
  }
  .style1 a:hover {
    color: #000000;
  }
  .style1 a:active {
    color: #000000;
  }
  /* 中文版顯示以下樣式 */
  table.green {
    border: 1px solid #00a5c0;
  }
  table.green tr:first-child{
    background: #00a5c0;
  }
  table.green .style1 a:link {
    color: #00a5c0;
  }
  /* 英文版顯示以下樣式 */
  table.gray {
    border: 1px solid #526366;
  }
  table.gray tr:first-child{
    background: #526366;
  }
  table.gray .style1 a:link {
    color: #526366;
  }
  
  /* 標籤樣式 */
  .tab-container {
    margin: 20px 0;
  }
  .tab-buttons {
    display: flex;
    border-bottom: 2px solid #ddd;
  }
  .tab-button {
    padding: 10px 20px;
    background: #f0f0f0;
    border: 1px solid #ddd;
    border-bottom: none;
    cursor: pointer;
    margin-right: 5px;
    font-weight: bold;
  }
  .tab-button.active {
    background: #4CAF50;
    color: white;
  }
  .tab-button.history {
    background: #808080;
    color: white;
  }
  
  /* 匯入按鈕樣式 */
  .import-button {
    background: #4CAF50;
    color: white;
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: bold;
    margin: 10px 0;
  }
  .import-button:hover {
    background: #45a049;
  }
  
  /* 搜尋區域樣式 */
  .search-container {
    background: #f9f9f9;
    padding: 15px;
    margin: 10px 0;
    border: 1px solid #ddd;
    border-radius: 4px;
    width: 85%;
  }
  
  /* 下載按鈕樣式 */
  .download-btn {
    color: #007bff;
    text-decoration: none;
    font-size: 16px;
  }
  .download-btn:hover {
    color: #0056b3;
  }
  
  /* 全部下載按鈕 */
  .download-all-btn {
    background: #007bff;
    color: white;
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: bold;
    display: inline-block;
    margin: 10px 0;
  }
  .download-all-btn:hover {
    background: #0056b3;
  }

  /* 分頁控制樣式 */
  .pagination-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #f8f9fa;
    border-radius: 8px;
    margin: 10px 0;
    padding: 15px 20px;
    border: 1px solid #dee2e6;
    position: relative;
    clear: both;
    width: 100%;
    box-sizing: border-box;
  }

  .pagination-controls {
    display: flex;
    align-items: center;
    gap: 15px;
  }

  .pagination-nav {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .page-btn {
    display: inline-flex;
    align-items: center;
    padding: 8px 12px;
    background: #007bff;
    color: white;
    text-decoration: none;
    border-radius: 5px;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
    border: none;
    cursor: pointer;
  }

  .page-btn:hover {
    background: #0056b3;
    color: white;
    text-decoration: none;
  }

  .page-btn:disabled,
  .page-btn.disabled {
    background: #6c757d;
    color: #fff;
    cursor: not-allowed;
    opacity: 0.6;
  }

  .page-btn.disabled:hover {
    background: #6c757d;
  }

  .page-input-group {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .page-input {
    width: 60px;
    padding: 6px 8px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    text-align: center;
    font-size: 14px;
  }

  .page-input:focus {
    border-color: #007bff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
  }

  .page-size-group {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .page-size-select {
    padding: 6px 8px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
    background: white;
  }

  .page-size-select:focus {
    border-color: #007bff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
  }

  .pagination-info {
    color: #6c757d;
    font-size: 14px;
  }

  .page-icon {
    margin-right: 4px;
  }

  /* 確保分頁控制在正確位置 */
  .pagination-container::before {
    content: "";
    display: table;
    clear: both;
  }

  /* 搜尋表單樣式 */
  .search-container {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 20px;
    margin: 15px 0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  }

  .search-form {
    display: flex;
    flex-direction: column;
    gap: 15px;
  }

  .search-row {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .search-label {
    font-weight: bold;
    color: #495057;
    min-width: 80px;
    font-size: 14px;
  }

  .search-input {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  }

  .search-input:focus {
    border-color: #007bff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
  }

  .date-range {
    display: flex;
    align-items: center;
    gap: 10px;
    flex: 1;
  }

  .date-input {
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
    width: 150px;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  }

  .date-input:focus {
    border-color: #007bff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
  }

  .date-separator {
    color: #6c757d;
    font-weight: bold;
    font-size: 16px;
  }

  .search-buttons {
    display: flex;
    gap: 10px;
    margin-left: 90px; /* 對齊標籤位置 */
  }

  .btn {
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.15s ease-in-out;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 5px;
  }

  .btn-primary {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    box-shadow: 0 2px 4px rgba(0,123,255,0.3);
  }

  .btn-primary:hover {
    background: linear-gradient(135deg, #0056b3, #004085);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,123,255,0.4);
  }

  .btn-secondary {
    background: linear-gradient(135deg, #6c757d, #545b62);
    color: white;
    box-shadow: 0 2px 4px rgba(108,117,125,0.3);
  }

  .btn-secondary:hover {
    background: linear-gradient(135deg, #545b62, #3d4142);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(108,117,125,0.4);
  }

  /* 響應式設計 */
  @media (max-width: 768px) {
    .search-row {
      flex-direction: column;
      align-items: flex-start;
    }

    .search-label {
      min-width: auto;
    }

    .search-input, .date-range {
      width: 100%;
    }

    .search-buttons {
      margin-left: 0;
      justify-content: center;
    }

    .date-range {
      flex-direction: column;
      gap: 5px;
    }

    .date-input {
      width: 100%;
    }
  }

</style>
<script src="../js/jquery.js"></script>
<script type="text/javascript">
  function MM_preloadImages() { //v3.0
    var d=document; if(d.images){ if(!d.MM_p) d.MM_p=new Array();
    var i,j=d.MM_p.length,a=MM_preloadImages.arguments; for(i=0; i<a.length; i++)
    if (a[i].indexOf("#")!=0){ d.MM_p[j]=new Image; d.MM_p[j++].src=a[i];}}
  }
  function MM_swapImgRestore() { //v3.0
    var i,x,a=document.MM_sr; for(i=0;a&&i<a.length&&(x=a[i])&&x.oSrc;i++) x.src=x.oSrc;
  }
  function MM_findObj(n, d) { //v4.01
    var p,i,x;  if(!d) d=document; if((p=n.indexOf("?"))>0&&parent.frames.length) {
    d=parent.frames[n.substring(p+1)].document; n=n.substring(0,p);}
    if(!(x=d[n])&&d.all) x=d.all[n]; for (i=0;!x&&i<d.forms.length;i++) x=d.forms[i][n];
    for(i=0;!x&&d.layers&&i<d.layers.length;i++) x=MM_findObj(n,d.layers[i].document);
    if(!x && d.getElementById) x=d.getElementById(n); return x;
  }
  function MM_swapImage() { //v3.0
    var i,j=0,x,a=MM_swapImage.arguments; document.MM_sr=new Array; for(i=0;i<(a.length-2);i+=3)
    if ((x=MM_findObj(a[i]))!=null){document.MM_sr[j++]=x; if(!x.oSrc) x.oSrc=x.src; x.src=a[i+2];}
  }
  // 舊的goPage函數已移除，使用新的分頁系統
  function goToUrl(url, langID) {
    // 直接跳轉到指定URL，如果需要langID參數則加入
    if(langID && langID !== ""){
      var separator = url.includes('?') ? '&' : '?';
      url += separator + 'lang_id=' + encodeURIComponent(langID);
    }
    window.location.href = url;
  }
  function switchTab(tab) {
    var url = new URL(window.location);
    url.searchParams.set('tab', tab);
    url.searchParams.set('page', 1);
    // 保持當前的pageSize和搜尋條件
    window.location.href = url.toString();
  }
  function importFile() {
    // 跳轉到匯入頁面
    window.location.href = 'ConvertImport.php';
  }
  function downloadAll() {
    // 全部下載功能 - 從URL參數獲取tab值
    var urlParams = new URLSearchParams(window.location.search);
    var tab = urlParams.get('tab') || 'current';
    window.location.href = 'ConvertDownload.php?action=all&type=' + tab;
  }
  function downloadFile(filename) {
    // 單一XML檔案下載功能 - 從URL參數獲取tab值
    var urlParams = new URLSearchParams(window.location.search);
    var tab = urlParams.get('tab') || 'current';
    window.location.href = 'ConvertDownload.php?action=single&filename=' + encodeURIComponent(filename) + '&type=' + tab;
  }
</script>
</head>

<body onload="MM_preloadImages('../images/control_bt02_01.gif','../images/control_bt02_02.gif','../images/control_bt_02.gif')">
  <table width="960" border="0" align="center" cellpadding="0" cellspacing="0">
    <tr>
      <th colspan="2" scope="row"><img src="../images/control_01.gif" width="960" height="109" /></th>
    </tr>
    <tr>
      <th width="150" valign="top" bgcolor="#FFFFFF" scope="row">
        <table width="140" border="0" cellspacing="0" cellpadding="0">
    <?php include ("../left.php"); ?>
        </table>
      </th>
      <td width="810" valign="top" bgcolor="#FFFFFF">
        <table width="810" border="0" cellspacing="0" cellpadding="0">
          <tr>
            <th height="50" align="left" scope="row"><span class="style1">【轉檔工具】</span></th>
          </tr>
          <tr>
            <th align="left" valign="top" scope="row">
              <div style="display: flex; justify-content: space-between; align-items: center;">
                <!-- 匯入按鈕 -->
                <button class="import-button" onclick="importFile()">匯入</button>
                <!-- 搜尋區域 -->
                <div class="search-container">
                  <form name="searchForm" method="get">
                    <input type="hidden" name="tab" value="<?php echo $tab; ?>">
                    <input type="hidden" name="pageSize" value="<?php echo $pageSize; ?>">
                    <div class="search-form">
                      <!-- 第一行：關鍵字搜尋 -->
                      <div class="search-row">
                        <label class="search-label">搜尋條件：</label>
                        <input type="text" name="search_keyword" value="<?php echo htmlspecialchars($search_keyword); ?>" placeholder="請輸入關鍵字" class="search-input">
                      </div>

                      <!-- 第二行：日期範圍 -->
                      <div class="search-row">
                        <label class="search-label">建立日期：</label>
                        <div class="date-range">
                          <input type="date" name="search_date_from" value="<?php echo htmlspecialchars($search_date_from); ?>" class="date-input">
                          <span class="date-separator">~</span>
                          <input type="date" name="search_date_to" value="<?php echo htmlspecialchars($search_date_to); ?>" class="date-input">
                        </div>
                      </div>

                      <!-- 第三行：按鈕 -->
                      <div class="search-row">
                        <div class="search-buttons">
                          <button type="submit" class="btn btn-primary">🔍 搜尋</button>
                          <?php if (!empty($search_keyword) || !empty($search_date_from) || !empty($search_date_to)): ?>
                          <button type="button" class="btn btn-secondary" onclick="clearSearch()">✖ 清除</button>
                          <?php endif; ?>
                        </div>
                      </div>
                    </div>
                  </form>
                </div>
              </div>
              
              <!-- 標籤切換 -->
              <div class="tab-container">
                <div class="tab-buttons">
                  <div class="tab-button <?php echo ($tab == 'current') ? 'active' : ''; ?>" onclick="switchTab('current')">
                    Current(<?php echo $currentTotal?>)
                  </div>
                  <div class="tab-button <?php echo ($tab == 'history') ? 'history' : ''; ?>" onclick="switchTab('history')">
                    History(<?php echo $historyTotal?>)
                  </div>
                </div>
              </div>
            </th>
          </tr>
          <tr>
            <th align="center" scope="row">
              <table width="780" border="1" align="center" cellpadding="0" cellspacing="1" class="<?php echo $show_color;?>">
                <tr>
                  <td height="30" align="center">
                    <span class="style2">檔案名稱</span>
                  </td>
                  <td height="30" align="center" scope="row">
                    <span class="style2">Creation Date</span>
                  </td>
                  <td height="30" align="center">
                    <span class="style2">Download</span>
                  </td>
                </tr>
          <?php
          foreach($currentData as $item) {
              $displayCylinderNo = !empty($item['cylinder_no']) ? $item['cylinder_no'] : '';
              $cylinderClass = !empty($item['cylinder_no']) ? 'red-text' : '';
          ?>
                <tr>
                  <td height="30" width="60%" align="center" class="style1 <?php echo $cylinderClass; ?>">
                    <?php echo $item['filename']; ?>
                  </td>
                  <td height="30" width="30%" align="center" class="style1">
                    <?php echo $item['creation_date']; ?>
                  </td>
                  <td height="30" width="10%" align="center" class="style1">
                    <a href="#" class="download-btn" onclick="downloadFile('<?php echo $item['filename']; ?>'); return false;">⬇</a>
                  </td>
                </tr>
          <?php
          }
          ?>
              </table>
              <!-- 表格底部操作區 -->
              <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 10px;">
                <div style="font-size: 12px; color: #666;">
                  共 <?php echo number_format($totalRecords); ?> 筆
                </div>
                <button class="download-all-btn" onclick="downloadAll(); return false;">全部下載 ⬇</button>
              </div>
            </th>
          </tr>
          <!-- 美化的分頁控制 -->
          <tr>
            <th colspan="1" scope="row">
              <div class="pagination-container">
              <div class="pagination-info">
                顯示第 <?php echo (($page-1) * $pageSize + 1); ?> - <?php echo min($page * $pageSize, $totalRecords); ?> 筆，共 <?php echo number_format($totalRecords); ?> 筆
              </div>

              <div class="pagination-controls">
                <!-- 頁面大小選擇 -->
                <div class="page-size-group">
                  <label>每頁顯示：</label>
                  <select class="page-size-select" onchange="changePageSize(this.value)">
                    <option value="20" <?php echo ($pageSize == 20) ? 'selected' : ''; ?>>20筆</option>
                    <option value="50" <?php echo ($pageSize == 50) ? 'selected' : ''; ?>>50筆</option>
                    <option value="100" <?php echo ($pageSize == 100) ? 'selected' : ''; ?>>100筆</option>
                    <option value="200" <?php echo ($pageSize == 200) ? 'selected' : ''; ?>>200筆</option>
                  </select>
                </div>

                <!-- 分頁導航 -->
                <div class="pagination-nav">
                  <!-- 上一頁按鈕 -->
                  <?php if ($page <= 1): ?>
                    <button class="page-btn disabled" disabled>
                      <span class="page-icon">◀</span> 上一頁
                    </button>
                  <?php else: ?>
                    <button class="page-btn" onclick="goPage(<?php echo $page-1; ?>)">
                      <span class="page-icon">◀</span> 上一頁
                    </button>
                  <?php endif; ?>

                  <!-- 頁面輸入 -->
                  <div class="page-input-group">
                    <span>第</span>
                    <input type="number" class="page-input" id="pageInput" value="<?php echo $page; ?>" min="1" max="<?php echo $totalPages; ?>" onkeypress="handlePageInput(event)">
                    <span>頁 / <?php echo $totalPages; ?>頁</span>
                    <button class="page-btn" onclick="goToInputPage()" style="padding: 6px 10px; font-size: 12px;">跳轉</button>
                  </div>

                  <!-- 下一頁按鈕 -->
                  <?php if ($page >= $totalPages): ?>
                    <button class="page-btn disabled" disabled>
                      下一頁 <span class="page-icon">▶</span>
                    </button>
                  <?php else: ?>
                    <button class="page-btn" onclick="goPage(<?php echo $page+1; ?>)">
                      下一頁 <span class="page-icon">▶</span>
                    </button>
                  <?php endif; ?>
                </div>
              </div>
              </div>
            </th>
          </tr>
        </table>
      </td>
    </tr>
  </table>

<script>
function clearSearch() {
  // 清除搜尋條件
  window.location.href = 'ConvertList.php?tab=<?php echo $tab; ?>';
}

// 分頁跳轉函數
function goPage(page) {
  var url = new URL(window.location);
  url.searchParams.set('page', page);
  window.location.href = url.toString();
}

// 改變頁面大小
function changePageSize(pageSize) {
  var url = new URL(window.location);
  url.searchParams.set('pageSize', pageSize);
  url.searchParams.set('page', 1); // 重置到第一頁
  window.location.href = url.toString();
}

// 處理頁面輸入框的Enter鍵
function handlePageInput(event) {
  if (event.key === 'Enter') {
    goToInputPage();
  }
}

// 跳轉到輸入的頁面
function goToInputPage() {
  var pageInput = document.getElementById('pageInput');
  var page = parseInt(pageInput.value);
  var maxPage = <?php echo $totalPages; ?>;

  if (isNaN(page) || page < 1) {
    alert('請輸入有效的頁碼（1-' + maxPage + '）');
    pageInput.value = <?php echo $page; ?>;
    return;
  }

  if (page > maxPage) {
    alert('頁碼不能超過 ' + maxPage);
    pageInput.value = <?php echo $page; ?>;
    return;
  }

  goPage(page);
}

// 頁面載入時的初始化
document.addEventListener('DOMContentLoaded', function() {
  // 為頁面輸入框添加焦點樣式
  var pageInput = document.getElementById('pageInput');
  if (pageInput) {
    pageInput.addEventListener('focus', function() {
      this.select();
    });
  }
});
</script>

</body>
</html>
