<?php
header('Content-Type: application/json; charset=utf-8');
ini_set('display_errors','off');

require_once($_SERVER["DOCUMENT_ROOT"]."/resonac-all/sysconfig/config.php");
require_once(LIBRARY_DIR."library/classes/admin/Control.php");
require_once(LIBRARY_DIR."library/classes/admin/Account.php");

// 檢查登入狀態（暫時關閉以便測試）
/*
try {
    Authenticator::isAccountLogin("ss_account");
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => '請先登入']);
    exit;
}
*/

// 資料庫連接
$oDB = new DB(DB_HOST, DB_NAME, DB_USER, DB_PASSWORD, true);
if (!$oDB->open()) {
    echo json_encode(['success' => false, 'message' => '資料庫連接失敗']);
    exit;
}

// 處理POST請求中的JSON資料
$input = null;
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $input = json_decode(file_get_contents('php://input'), true);
}

$action = $_GET['action'] ?? ($input['action'] ?? '');
$userId = $_SESSION['ss_account']['account'] ?? 'admin';

try {
    switch ($action) {
        case 'getTypes':
            getInspectionTypes($oDB);
            break;
            
        case 'getSettings':
            $typeId = intval($_GET['typeId'] ?? 0);
            getInspectionSettings($oDB, $typeId);
            break;
            
        case 'saveSettings':
            $typeId = intval($input['typeId'] ?? 0);
            $settings = $input['settings'] ?? [];
            saveInspectionSettings($oDB, $typeId, $settings, $userId);
            break;

        case 'addType':
            $materialNumber = trim($input['materialNumber'] ?? '');
            addInspectionType($oDB, $materialNumber, $userId);
            break;
            
        default:
            echo json_encode(['success' => false, 'message' => '無效的操作']);
            break;
    }
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}

/**
 * 取得轉檔類型列表
 */
function getInspectionTypes($oDB) {
    $sql = "SELECT id, type_name, type_code, description FROM convert_inspection_types WHERE is_active = 1 ORDER BY id";
    $result = $oDB->execute($sql, []);
    
    $types = [];
    if ($result) {
        while ($oDB->fetchRow()) {
            $types[] = $oDB->rowArray;
        }
    }
    
    echo json_encode(['success' => true, 'types' => $types]);
}

/**
 * 取得指定類型的檢驗項目設定
 */
function getInspectionSettings($oDB, $typeId) {
    if ($typeId <= 0) {
        echo json_encode(['success' => false, 'message' => '無效的類型ID']);
        return;
    }
    
    $sql = "SELECT id, item_name, specification, detection_limit, ucl, sort_order 
            FROM convert_inspection_settings 
            WHERE type_id = :typeId AND is_active = 1 
            ORDER BY sort_order, id";
    $result = $oDB->execute($sql, [':typeId' => $typeId]);
    
    $settings = [];
    if ($result) {
        while ($oDB->fetchRow()) {
            $settings[] = $oDB->rowArray;
        }
    }
    
    echo json_encode(['success' => true, 'settings' => $settings]);
}

/**
 * 儲存檢驗項目設定
 */
function saveInspectionSettings($oDB, $typeId, $settings, $userId) {
    if ($typeId <= 0) {
        echo json_encode(['success' => false, 'message' => '無效的類型ID']);
        return;
    }
    
    // 開始交易
    $oDB->execute("START TRANSACTION", []);
    
    try {
        // 先刪除現有設定
        $deleteSql = "DELETE FROM convert_inspection_settings WHERE type_id = :typeId";
        $oDB->execute($deleteSql, [':typeId' => $typeId]);
        
        // 插入新設定
        $insertSql = "INSERT INTO convert_inspection_settings (type_id, item_name, specification, detection_limit, ucl, sort_order) 
                    VALUES (:typeId, :itemName, :specification, :detectionLimit, :ucl, :sortOrder)";
        
        foreach ($settings as $index => $setting) {
            $params = [
                ':typeId' => $typeId,
                ':itemName' => $setting['itemName'],
                ':specification' => $setting['specification'],
                ':detectionLimit' => $setting['detectionLimit'],
                ':ucl' => $setting['ucl'],
                ':sortOrder' => $index + 1
            ];
            
            $result = $oDB->execute($insertSql, $params);
            if (!$result) {
                throw new Exception('儲存設定失敗');
            }
        }
        
        // 提交交易
        $oDB->execute("COMMIT", []);
        echo json_encode(['success' => true, 'message' => '設定已儲存']);
        
    } catch (Exception $e) {
        // 回滾交易
        $oDB->execute("ROLLBACK", []);
        throw $e;
    }
}

/**
 * 新增轉檔類型
 */
function addInspectionType($oDB, $materialNumber, $userId) {
    if (empty($materialNumber)) {
        echo json_encode(['success' => false, 'message' => 'Material Number不能為空']);
        return;
    }

    // 檢查是否已存在相同的Material Number
    $checkSql = "SELECT COUNT(*) as count FROM convert_inspection_types WHERE type_name = :materialNumber";
    $checkResult = $oDB->execute($checkSql, [':materialNumber' => $materialNumber]);

    if ($checkResult && $oDB->fetchRow() && $oDB->rowArray['count'] > 0) {
        echo json_encode(['success' => false, 'message' => '此Material Number已存在']);
        return;
    }

    try {
        // 生成type_code（將Material Number轉為小寫並替換空格為底線）
        $typeCode = strtolower(str_replace([' ', '-', '.'], '_', $materialNumber));

        $insertSql = "INSERT INTO convert_inspection_types (type_name, type_code, description, created_by)
                    VALUES (:typeName, :typeCode, :description, :createdBy)";
        $insertParams = [
            ':typeName' => $materialNumber,
            ':typeCode' => $typeCode,
            ':description' => "Material Number: {$materialNumber} 的檢驗項目設定",
            ':createdBy' => $userId
        ];

        $result = $oDB->execute($insertSql, $insertParams);
        if ($result) {
            echo json_encode(['success' => true, 'message' => '轉檔類型已新增']);
        } else {
            echo json_encode(['success' => false, 'message' => '新增失敗']);
        }

    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => '新增時發生錯誤: ' . $e->getMessage()]);
    }
}
?>
