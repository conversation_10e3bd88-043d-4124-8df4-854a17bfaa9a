# CSV轉XML系統部署指南

## 🚀 新環境部署步驟

### 1. 檔案部署
將整個 `backend/CONVERT/` 目錄複製到新環境的對應位置。

### 2. 目錄權限設置
確保以下目錄有寫入權限：
```bash
chmod 755 /resonac-all/uploads/convert/
mkdir -p /resonac-all/uploads/convert/current/
mkdir -p /resonac-all/uploads/convert/history/
chmod 777 /resonac-all/uploads/convert/current/
chmod 777 /resonac-all/uploads/convert/history/
```

### 3. 資料庫安裝
**只需執行一個檔案：**
```
訪問：backend/CONVERT/install_database.php
```

這個檔案會自動完成：
- ✅ 建立所有必要的資料表
- ✅ 插入預設轉檔類型 (M001, M002, CHEM-A)
- ✅ 為M001插入預設檢驗項目 (H2O, CO2, O2, PURITY)

### 4. 驗證安裝
安裝完成後訪問：
```
backend/CONVERT/ConvertList.php
```

## 📋 系統需求

### PHP擴展
確保已安裝：
- `DOMDocument` - XML生成
- `ZipArchive` - ZIP檔案打包
- `SimpleXML` - XML解析

### 資料庫
- MySQL 5.7+ 或 MariaDB 10.2+

## 🗂️ 資料庫結構

安裝後會建立以下資料表：

### convert_batch
批次管理表，記錄每次轉檔的批次資訊

### convert_files  
檔案記錄表，記錄生成的XML檔案資訊

### convert_inspection_types
轉檔類型表，對應不同的MaterialNumber

### convert_inspection_settings
檢驗項目設定表，每個轉檔類型的檢驗項目和UCL設定

## 🎯 預設資料

### 轉檔類型
- **M001**: 包含 H2O, CO2, O2, PURITY 檢驗項目
- **M002**: 空白設定，可自行新增檢驗項目  
- **CHEM-A**: 空白設定，可自行新增檢驗項目

### 檢驗項目範例 (M001)
| 項目 | 規格 | 檢測限 | UCL | Unit |
|------|------|--------|-----|------|
| H2O | <=10 | 0.5 | 1.0 | volppm |
| CO2 | <=5 | 0.1 | 0.8 | volppm |
| O2 | <=2 | 0.05 | 0.5 | volppm |
| PURITY | >=99 | 0.1 | 100.0 | vol% |

## 🔧 使用流程

1. **設定檢驗項目**：在匯入頁面設定各MaterialNumber的檢驗項目
2. **準備CSV檔案**：確保包含MaterialNumber欄位
3. **上傳轉檔**：系統自動根據MaterialNumber匹配檢驗項目設定
4. **下載XML**：生成符合Micron_COA格式的XML檔案

## 🚨 注意事項

- 確保資料庫連接設定正確
- 檔案上傳目錄必須有寫入權限
- 建議定期備份資料庫
- CSV檔案必須包含MaterialNumber欄位用於匹配

## 📞 故障排除

如果安裝失敗：
1. 檢查資料庫連接設定
2. 確認資料庫使用者有建表權限
3. 檢查PHP錯誤日誌
4. 確認目錄權限設定正確
