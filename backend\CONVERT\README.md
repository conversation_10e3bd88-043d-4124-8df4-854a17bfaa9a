# CSV轉XML工具系統

## 📋 系統概述

這是一個CSV轉XML的轉檔工具系統，將CSV檔案轉換為XML格式並儲存在伺服器上，提供檔案管理和下載功能。

## 🚀 快速部署

**新環境部署只需執行一個檔案：**
```
訪問：backend/CONVERT/install_database.php
```

詳細部署說明請參考：[DEPLOYMENT.md](DEPLOYMENT.md)

## 📁 檔案結構

```
backend/CONVERT/
├── ConvertList.php         # 主要列表頁面
├── ConvertImport.php       # 檔案匯入頁面
├── ConvertDownload.php     # 檔案下載處理
├── common.php             # 共用函數庫
├── database_schema.sql    # 資料庫結構
├── install_database.php   # 資料庫安裝腳本
├── test.php              # 測試頁面
└── README.md             # 說明文件
```

## 🎯 主要功能

### 1. CSV轉XML轉換
- **CSV上傳**：支援CSV格式檔案上傳
- **自動轉換**：第一行作為XML標籤，每行生成獨立XML檔案
- **檔案儲存**：XML檔案儲存在伺服器檔案系統中
- **格式驗證**：自動驗證CSV格式和內容

### 2. 檔案管理
- **Current（當前）** 和 **History（歷史）** 標籤切換
- 分頁顯示，每頁20筆檔案
- 搜尋功能（支援檔名、鋼瓶編號搜尋）
- 自動清理超過一年的檔案

### 3. 檔案下載
- 單一XML檔案下載
- 全部XML檔案下載（ZIP格式）
- 檔案移動到歷史目錄

## 📊 資料庫結構

### convert_data（主要資料表）
| 欄位 | 類型 | 說明 |
|------|------|------|
| id | int(11) | 主鍵 |
| cylinder_no | varchar(50) | 鋼瓶編號 |
| seq | varchar(10) | 序號 |
| creation_date | datetime | 建立日期 |
| status | enum | 狀態（processing/completed/failed） |
| data_type | enum | 資料類型（current/history） |
| created_by | varchar(50) | 建立者 |

### convert_detail_data（詳細資料表）
| 欄位 | 類型 | 說明 |
|------|------|------|
| id | int(11) | 主鍵 |
| convert_data_id | int(11) | 關聯主表ID |
| row_seq | int(11) | 資料行序號 |
| data1/data2/data3 | decimal(10,2) | 測量數據 |
| temperature | decimal(5,2) | 溫度 |
| pressure | decimal(8,2) | 壓力 |
| flow_rate | decimal(8,2) | 流量 |

## 📝 CSV轉XML格式

### CSV輸入格式
```csv
CylinderNo,Seq,CreationDate,Temperature,Pressure,FlowRate,Status
SGU29330,001,2025/7/10 10:29:01,25.5,1013.25,15.2,completed
SGU29388,002,2025/7/11 17:29:01,26.0,1014.30,16.3,completed
```

### 生成的XML格式
```xml
<?xml version="1.0" encoding="UTF-8"?>
<ConvertData>
  <Metadata>
    <CreatedBy>admin</CreatedBy>
    <CreatedAt>2025-01-08 10:30:00</CreatedAt>
  </Metadata>
  <Data>
    <CylinderNo>SGU29330</CylinderNo>
    <Seq>001</Seq>
    <CreationDate>2025/7/10 10:29:01</CreationDate>
    <Temperature>25.5</Temperature>
    <Pressure>1013.25</Pressure>
    <FlowRate>15.2</FlowRate>
    <Status>completed</Status>
  </Data>
</ConvertData>
```

### 轉換規則
- **第一行**：作為XML標籤名稱
- **每一行**：生成一個獨立的XML檔案
- **檔案命名**：convert_001.xml, convert_002.xml...
- **標籤清理**：特殊字符轉換為底線，確保XML規範

## 🔧 API函數說明

### common.php 主要函數

#### getConvertList($oDB, $type, $keyword, $page, $pageSize)
取得轉檔資料列表
- `$oDB`: 資料庫連接物件
- `$type`: 資料類型（current/history）
- `$keyword`: 搜尋關鍵字
- `$page`: 頁數
- `$pageSize`: 每頁筆數

#### importCSVFile($oDB, $file, $userId)
匯入CSV檔案
- `$oDB`: 資料庫連接物件
- `$file`: 上傳檔案資訊
- `$userId`: 使用者ID

#### cleanOldData($oDB)
清理超過一年的資料
- `$oDB`: 資料庫連接物件

## 🎨 前端功能

### 標籤切換
- Current（當前）：綠色標籤
- History（歷史）：灰色標籤

### 搜尋功能
- 即時搜尋鋼瓶編號、序號、日期
- 支援清除搜尋條件

### 下載功能
- 單一檔案：點擊下載圖示
- 全部下載：點擊「全部下載」按鈕

## 🔒 安全性

- 登入驗證：使用現有的帳號系統
- 檔案驗證：只允許CSV格式
- SQL注入防護：使用參數化查詢
- 檔案大小限制：建議設定上傳限制

## 🐛 故障排除

### 常見問題

1. **資料表不存在**
   - 執行 `install_database.php` 建立資料表

2. **匯入失敗**
   - 檢查CSV格式是否正確
   - 確認必填欄位不為空

3. **下載失敗**
   - 檢查臨時目錄權限
   - 確認ZipArchive擴展已安裝

4. **搜尋無結果**
   - 檢查資料庫連接
   - 確認搜尋關鍵字格式

## 📞 技術支援

如有問題請檢查：
1. PHP錯誤日誌
2. 資料庫連接狀態
3. 檔案權限設定
4. 瀏覽器控制台錯誤訊息
