# 地區檔名設定功能說明

## 🌍 功能概述

轉檔工具現在支援根據不同地區產生不同的檔名前綴，讓同一個系統可以為不同地區的客戶產生符合當地命名規則的檔案。

## 🚀 安裝步驟

### 1. 執行資料庫更新
訪問以下網址來更新資料庫結構並建立地區專用帳號：
```
backend/CONVERT/update_account_region.php
```

這個腳本會自動完成：
- ✅ 在ACCOUNT表新增REGION欄位
- ✅ 建立三個地區專用帳號
- ✅ 設定預設地區為新加坡(SG)

### 2. 驗證安裝
安裝完成後可以訪問：
```
backend/CONVERT/ConvertImport.php
```
在匯入頁面下方可以看到當前使用者的地區設定資訊。

## 👥 地區專用帳號

系統會自動建立以下三個帳號：

| 帳號 | 密碼 | 地區 | 檔名前綴 |
|------|------|------|----------|
| singapore_user | singapore123 | SG (新加坡) | SG08, SG18, SG28 |
| japan_user | japan123 | JP (日本) | JP08, JP18, JP28 |
| usa_user | usa123 | US (美國) | US08, US18, US28 |

## 📁 檔名規則

### 原始格式
```
{prefix}_1039151_{MaterialNumber}_{LotNumber}_{CylinderNumber}.xml
```

### 各地區範例
- **新加坡**: `SG08_1039151_M001_LOT123_CYL456.xml`
- **日本**: `JP08_1039151_M001_LOT123_CYL456.xml`
- **美國**: `US08_1039151_M001_LOT123_CYL456.xml`

## 🔧 使用方式

### 1. 登入對應地區帳號
使用上述地區專用帳號登入系統。

### 2. 執行轉檔作業
正常使用轉檔功能，系統會自動根據登入帳號的地區設定產生對應的檔名前綴。

### 3. 查看地區設定
在ConvertImport.php匯入頁面下方可以查看：
- 當前使用者的地區設定
- 對應的檔名前綴
- 檔名格式說明

## ⚙️ 技術實作

### 資料庫變更
```sql
-- 新增REGION欄位到ACCOUNT表
ALTER TABLE ACCOUNT ADD COLUMN REGION VARCHAR(10) DEFAULT 'SG' 
COMMENT '地區代碼 SG:新加坡 JP:日本 US:美國';
```

### 程式碼變更
1. **Account.php**: 新增region屬性和相關方法
2. **common.php**: 新增`getRegionPrefixes()`函數
3. **ConvertImport.php**: 修正使用者ID取得方式，並在頁面下方顯示地區資訊
4. **ConvertDownload.php**: 修正使用者ID取得方式

### 地區代碼對應
```php
switch ($region) {
    case 'JP':
        return ['JP08', 'JP18', 'JP28'];
    case 'US':
        return ['US08', 'US18', 'US28'];
    case 'SG':
    default:
        return ['SG08', 'SG18', 'SG28'];
}
```

## 🔒 安全考量

1. **預設值**: 如果無法取得使用者地區設定，系統會使用新加坡(SG)作為預設值
2. **錯誤處理**: 資料庫查詢失敗時會記錄錯誤並使用預設前綴
3. **權限控制**: 地區設定功能需要登入才能使用

## 📋 維護說明

### 新增地區
如需新增其他地區，請：
1. 在`getRegionPrefixes()`函數中新增對應的case
2. 建立對應的地區專用帳號
3. 更新說明文件

### 修改前綴
如需修改某地區的前綴，請：
1. 修改`getRegionPrefixes()`函數中對應的返回值
2. 更新說明文件

## 🐛 故障排除

### 檔名仍使用SG前綴
1. 確認已執行資料庫更新腳本
2. 確認使用正確的地區專用帳號登入
3. 檢查ACCOUNT表中該帳號的REGION欄位值

### 無法查看地區設定
1. 確認已登入系統
2. 確認RegionInfo.php檔案存在且可訪問
3. 檢查資料庫連接是否正常

## 📞 技術支援

如有任何問題，請檢查：
1. 資料庫連接狀態
2. 檔案權限設定
3. PHP錯誤日誌
4. 瀏覽器開發者工具的錯誤訊息
