<?php
ini_set('display_errors','on');
error_reporting(E_ALL);

require_once($_SERVER["DOCUMENT_ROOT"]."/resonac-all/sysconfig/config.php");
require_once(LIBRARY_DIR."library/classes/admin/Control.php");

echo "<h1>CSV轉XML工具 - 資料庫安裝</h1>";

// 測試資料庫連接
$oDB = new DB(DB_HOST, DB_NAME, DB_USER, DB_PASSWORD, true);
if (!$oDB->open()) {
    die("資料庫連接失敗: " . $oDB->error());
}
echo "<p style='color: green;'>✓ 資料庫連接成功</p>";

// 建立資料表
$tables = [
    'convert_batch' => "CREATE TABLE `convert_batch` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `batch_id` varchar(50) NOT NULL COMMENT '批次ID',
        `original_filename` varchar(255) NOT NULL COMMENT '原始CSV檔案名稱',
        `total_rows` int(11) DEFAULT 0 COMMENT '總行數',
        `success_count` int(11) DEFAULT 0 COMMENT '成功轉換數量',
        `failed_count` int(11) DEFAULT 0 COMMENT '失敗數量',
        `batch_status` enum('processing','completed','failed') DEFAULT 'processing' COMMENT '批次狀態',
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '建立時間',
        `created_by` varchar(50) DEFAULT NULL COMMENT '建立者',
        PRIMARY KEY (`id`),
        UNIQUE KEY `idx_batch_id` (`batch_id`),
        KEY `idx_created_at` (`created_at`),
        KEY `idx_created_by` (`created_by`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='轉檔批次表'",
    
    'convert_files' => "CREATE TABLE `convert_files` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `batch_id` varchar(50) NOT NULL COMMENT '批次ID',
        `filename` varchar(255) NOT NULL COMMENT 'XML檔案名稱',
        `file_path` varchar(500) NOT NULL COMMENT '檔案完整路徑',
        `file_size` int(11) DEFAULT NULL COMMENT '檔案大小(bytes)',
        `cylinder_no` varchar(50) DEFAULT NULL COMMENT '鋼瓶編號',
        `seq` varchar(10) NOT NULL COMMENT '序號',
        `xml_creation_date` datetime DEFAULT NULL COMMENT 'XML中的建立日期',
        `file_status` enum('created','uploaded','failed') DEFAULT 'created' COMMENT '檔案狀態',
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '記錄建立時間',
        PRIMARY KEY (`id`),
        KEY `idx_batch_id` (`batch_id`),
        KEY `idx_filename` (`filename`),
        KEY `idx_cylinder_no` (`cylinder_no`),
        KEY `idx_seq` (`seq`),
        KEY `idx_created_at` (`created_at`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='XML檔案記錄表'",
    


    'convert_inspection_types' => "CREATE TABLE `convert_inspection_types` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `type_name` varchar(100) NOT NULL COMMENT '轉檔類型名稱',
        `type_code` varchar(50) NOT NULL COMMENT '轉檔類型代碼',
        `description` text DEFAULT NULL COMMENT '描述',
        `is_active` tinyint(1) DEFAULT 1 COMMENT '是否啟用',
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '建立時間',
        `created_by` varchar(50) DEFAULT NULL COMMENT '建立者',
        PRIMARY KEY (`id`),
        UNIQUE KEY `idx_type_code` (`type_code`),
        KEY `idx_type_name` (`type_name`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='轉檔類型表'",

    'convert_inspection_settings' => "CREATE TABLE `convert_inspection_settings` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `type_id` int(11) NOT NULL COMMENT '轉檔類型ID',
        `item_name` varchar(100) NOT NULL COMMENT '檢驗項目名稱',
        `specification` varchar(100) DEFAULT NULL COMMENT '規格',
        `detection_limit` decimal(10,4) DEFAULT NULL COMMENT '檢測限',
        `ucl` decimal(10,4) DEFAULT NULL COMMENT '上限值',
        `sort_order` int(11) DEFAULT 0 COMMENT '排序',
        `is_active` tinyint(1) DEFAULT 1 COMMENT '是否啟用',
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '建立時間',
        `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新時間',
        PRIMARY KEY (`id`),
        KEY `idx_type_id` (`type_id`),
        KEY `idx_item_name` (`item_name`),
        CONSTRAINT `fk_inspection_settings_type` FOREIGN KEY (`type_id`) REFERENCES `convert_inspection_types` (`id`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='檢驗項目設定表'"
];

foreach ($tables as $tableName => $sql) {
    try {
        // 先檢查資料表是否存在
        $checkResult = $oDB->execute("SHOW TABLES LIKE '{$tableName}'", []);
        if ($checkResult && $oDB->queryRow > 0) {
            echo "<p style='color: orange;'>⚠ 資料表 {$tableName} 已存在，跳過建立</p>";
            continue;
        }
        
        // 建立資料表
        $result = $oDB->execute($sql, []);
        if ($result) {
            echo "<p style='color: green;'>✓ 成功建立資料表: {$tableName}</p>";
        } else {
            echo "<p style='color: red;'>✗ 建立資料表 {$tableName} 失敗: " . $oDB->error() . "</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ 建立資料表 {$tableName} 時發生錯誤: " . $e->getMessage() . "</p>";
    }
}

// 插入預設轉檔類型（MaterialNumber格式）
$defaultTypes = [
    ['type_name' => 'M001', 'type_code' => 'm001', 'description' => 'Material Number: M001 的檢驗項目設定'],
    ['type_name' => 'M002', 'type_code' => 'm002', 'description' => 'Material Number: M002 的檢驗項目設定'],
    ['type_name' => 'CHEM-A', 'type_code' => 'chem_a', 'description' => 'Material Number: CHEM-A 的檢驗項目設定']
];

foreach ($defaultTypes as $type) {
    try {
        $checkSql = "SELECT COUNT(*) as count FROM convert_inspection_types WHERE type_code = :typeCode";
        $checkResult = $oDB->execute($checkSql, [':typeCode' => $type['type_code']]);

        if ($checkResult && $oDB->fetchRow() && $oDB->rowArray['count'] == 0) {
            $insertSql = "INSERT INTO convert_inspection_types (type_name, type_code, description, created_by) VALUES (:typeName, :typeCode, :description, 'system')";
            $insertParams = [
                ':typeName' => $type['type_name'],
                ':typeCode' => $type['type_code'],
                ':description' => $type['description']
            ];

            if ($oDB->execute($insertSql, $insertParams)) {
                echo "<p style='color: green;'>✓ 成功建立轉檔類型: {$type['type_name']}</p>";
            }
        } else {
            echo "<p style='color: orange;'>⚠ 轉檔類型 {$type['type_name']} 已存在</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ 建立轉檔類型 {$type['type_name']} 時發生錯誤: " . $e->getMessage() . "</p>";
    }
}

// 為M001插入預設檢驗項目
try {
    $m001Id = null;
    $result = $oDB->execute("SELECT id FROM convert_inspection_types WHERE type_code = 'm001'", []);
    if ($result && $oDB->fetchRow()) {
        $m001Id = $oDB->rowArray['id'];
    }

    if ($m001Id) {
        // 檢查是否已有檢驗項目
        $checkItems = $oDB->execute("SELECT COUNT(*) as count FROM convert_inspection_settings WHERE type_id = :typeId", [':typeId' => $m001Id]);
        if ($checkItems && $oDB->fetchRow() && $oDB->rowArray['count'] == 0) {
            $defaultItems = [
                ['item_name' => 'H2O', 'specification' => '<=10', 'detection_limit' => 0.5, 'ucl' => 1.0],
                ['item_name' => 'CO2', 'specification' => '<=5', 'detection_limit' => 0.1, 'ucl' => 0.8],
                ['item_name' => 'O2', 'specification' => '<=2', 'detection_limit' => 0.05, 'ucl' => 0.5],
                ['item_name' => 'PURITY', 'specification' => '>=99', 'detection_limit' => 0.1, 'ucl' => 100.0]
            ];

            foreach ($defaultItems as $index => $item) {
                $itemSql = "INSERT INTO convert_inspection_settings (type_id, item_name, specification, detection_limit, ucl, sort_order) VALUES (:typeId, :itemName, :specification, :detectionLimit, :ucl, :sortOrder)";
                $itemParams = [
                    ':typeId' => $m001Id,
                    ':itemName' => $item['item_name'],
                    ':specification' => $item['specification'],
                    ':detectionLimit' => $item['detection_limit'],
                    ':ucl' => $item['ucl'],
                    ':sortOrder' => $index + 1
                ];

                if ($oDB->execute($itemSql, $itemParams)) {
                    echo "<p style='color: green;'>✓ 成功插入檢驗項目: {$item['item_name']}</p>";
                }
            }
        } else {
            echo "<p style='color: orange;'>⚠ M001 已有檢驗項目設定</p>";
        }
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>插入預設檢驗項目時發生錯誤: " . $e->getMessage() . "</p>";
}

echo "<h2>安裝完成</h2>";
echo "<p>系統已成功安裝，包含：</p>";
echo "<ul>";
echo "<li>✓ 資料庫表格建立</li>";
echo "<li>✓ 預設轉檔類型 (M001, M002, CHEM-A)</li>";
echo "<li>✓ M001 預設檢驗項目 (H2O, CO2, O2, PURITY)</li>";
echo "</ul>";
echo "<p><a href='ConvertList.php'>前往主頁面</a></p>";
?>
