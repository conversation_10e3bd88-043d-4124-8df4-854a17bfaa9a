<?php
/**
 * 簡化版地區欄位更新腳本
 * 專門處理CDATE欄位的問題
 */

require_once($_SERVER["DOCUMENT_ROOT"]."/resonac-all/sysconfig/config.php");
require_once(LIBRARY_DIR."library/classes/admin/Control.php");
require_once(LIBRARY_DIR."library/classes/admin/Account.php");

// 檢查是否已登入
Authenticator::isAccountLogin("ss_account");

$oDB = new DB(DB_HOST, DB_NAME, DB_USER, DB_PASSWORD, true);
if (!$oDB->open()) {
    die("資料庫連接失敗: " . $oDB->error());
}

echo "<h1>簡化版地區欄位更新</h1>";

try {
    // 步驟1: 檢查並新增REGION欄位
    echo "<h2>步驟1: 新增REGION欄位</h2>";
    
    $checkColumnSql = "SHOW COLUMNS FROM ACCOUNT LIKE 'REGION'";
    $result = $oDB->execute($checkColumnSql, []);
    
    if ($result && $oDB->queryRow > 0) {
        echo "<p style='color: orange;'>⚠ REGION欄位已存在</p>";
    } else {
        // 暫時設定寬鬆的SQL模式
        $oDB->execute("SET sql_mode = 'ALLOW_INVALID_DATES'", []);
        
        $addColumnSql = "ALTER TABLE ACCOUNT ADD COLUMN REGION VARCHAR(10) NULL COMMENT '地區代碼'";
        $result = $oDB->execute($addColumnSql, []);
        
        if ($result) {
            echo "<p style='color: green;'>✓ 成功新增REGION欄位</p>";
            
            // 設定預設值
            $updateSql = "UPDATE ACCOUNT SET REGION = 'SG' WHERE REGION IS NULL";
            $oDB->execute($updateSql, []);
            echo "<p style='color: green;'>✓ 已設定預設地區為SG</p>";
        } else {
            echo "<p style='color: red;'>✗ 新增REGION欄位失敗: " . $oDB->error() . "</p>";
            echo "<p style='color: blue;'>請嘗試手動執行SQL: ALTER TABLE ACCOUNT ADD COLUMN REGION VARCHAR(10);</p>";
        }
    }
    
    // 步驟2: 建立地區帳號
    echo "<h2>步驟2: 建立地區帳號</h2>";
    
    $accounts = [
        ['id' => 'japan_user', 'name' => '日本轉檔帳號', 'pwd' => 'japan123', 'region' => 'JP'],
        ['id' => 'usa_user', 'name' => '美國轉檔帳號', 'pwd' => 'usa123', 'region' => 'US']
    ];
    
    foreach ($accounts as $acc) {
        // 檢查帳號是否已存在
        $checkSql = "SELECT COUNT(*) as cnt FROM ACCOUNT WHERE ACCOUNT_ID = :id";
        $checkResult = $oDB->execute($checkSql, [':id' => $acc['id']]);
        
        if ($checkResult && $oDB->fetchRow() && $oDB->rowArray['cnt'] > 0) {
            echo "<p style='color: orange;'>⚠ 帳號 {$acc['id']} 已存在</p>";
            
            // 更新現有帳號的地區
            $updateRegionSql = "UPDATE ACCOUNT SET REGION = :region WHERE ACCOUNT_ID = :id";
            $updateResult = $oDB->execute($updateRegionSql, [':region' => $acc['region'], ':id' => $acc['id']]);
            
            if ($updateResult) {
                echo "<p style='color: green;'>✓ 已更新帳號 {$acc['id']} 的地區為 {$acc['region']}</p>";
            }
            continue;
        }
        
        // 建立新帳號 - 使用一般權限而非最大權限
        $insertSql = "INSERT INTO ACCOUNT (ACCOUNT_ID, NAME, PASSWD, STATUS, PRIV, CDATE, S_VALID_TIME, E_VALID_TIME, REGION)
                    VALUES (:id, :name, :pwd, '1', '0', CURDATE(), '00:00:00', '23:59:59', :region)";
        
        $params = [
            ':id' => $acc['id'],
            ':name' => $acc['name'],
            ':pwd' => md5($acc['pwd']),
            ':region' => $acc['region']
        ];
        
        $insertResult = $oDB->execute($insertSql, $params);
        
        if ($insertResult) {
            echo "<p style='color: green;'>✓ 成功建立帳號: {$acc['id']}</p>";
            echo "<p style='margin-left: 20px;'>帳號: {$acc['id']}</p>";
            echo "<p style='margin-left: 20px;'>密碼: {$acc['pwd']}</p>";
            echo "<p style='margin-left: 20px;'>地區: {$acc['region']}</p>";
        } else {
            echo "<p style='color: red;'>✗ 建立帳號 {$acc['id']} 失敗: " . $oDB->error() . "</p>";
        }
    }
    
    // 步驟3: 查看現有帳號的權限設定
    echo "<h2>步驟3: 現有帳號權限參考</h2>";

    $existingPrivSql = "SELECT ACCOUNT_ID, NAME, PRIV FROM ACCOUNT WHERE ACCOUNT_ID IN ('admin') OR ACCOUNT_ID LIKE '%user%' ORDER BY ACCOUNT_ID";
    $existingPrivResult = $oDB->execute($existingPrivSql, []);

    if ($existingPrivResult) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr style='background: #f0f0f0;'><th>帳號ID</th><th>名稱</th><th>權限(PRIV)</th></tr>";

        while ($oDB->fetchRow()) {
            $row = $oDB->rowArray;
            echo "<tr>";
            echo "<td>{$row['ACCOUNT_ID']}</td>";
            echo "<td>{$row['NAME']}</td>";
            echo "<td>{$row['PRIV']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }

    // 步驟4: 驗證結果
    echo "<h2>步驟4: 驗證結果</h2>";

    $verifySql = "SELECT ACCOUNT_ID, NAME, PRIV, REGION FROM ACCOUNT WHERE REGION IS NOT NULL ORDER BY REGION, ACCOUNT_ID";
    $verifyResult = $oDB->execute($verifySql, []);
    
    if ($verifyResult) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr style='background: #f0f0f0;'><th>帳號ID</th><th>名稱</th><th>權限</th><th>地區</th></tr>";

        while ($oDB->fetchRow()) {
            $row = $oDB->rowArray;
            echo "<tr>";
            echo "<td>{$row['ACCOUNT_ID']}</td>";
            echo "<td>{$row['NAME']}</td>";
            echo "<td>{$row['PRIV']}</td>";
            echo "<td>{$row['REGION']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h2>完成</h2>";
    echo "<p style='color: green;'>地區功能設定完成！</p>";
    echo "<p><a href='ConvertImport.php'>前往匯入頁面測試</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>發生錯誤: " . $e->getMessage() . "</p>";
}
?>
