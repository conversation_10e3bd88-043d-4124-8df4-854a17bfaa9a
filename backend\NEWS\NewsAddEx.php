<?php
// ini_set('display_errors','on');
require_once($_SERVER["DOCUMENT_ROOT"]."/resonac-all/sysconfig/config.php");
require_once(LIBRARY_DIR."library/classes/admin/Control.php");
require_once(LIBRARY_DIR."library/classes/admin/Account.php");
require_once(LIBRARY_DIR."library/classes/admin/Language.php");
require_once(LIBRARY_DIR."library/classes/admin/News.php");

Authenticator::isAccountLogin("ss_account");

$oDB = new DB(DB_HOST, DB_NAME, DB_USER, DB_PASSWORD, true);
if (!$oDB->open()) {
    die(CONNECTION_DB_FAILED.$oDB->error());
}

$oNews = new News($oDB);
$oLanguage = new Language();
$LanguageAry = $oLanguage->Load_Language("", 'array');

$msg = "";
$common_id = "";
foreach($LanguageAry as $key=>$langAry){
	$__lang_id = $langAry['lang_id'];
	$param = array();
    if($common_id == ""){ $common_id = $oNews->getTicketNO(); }
	$param['common_id'] = $common_id;
	$param['lang_id'] = htmlspecialchars(trim($__lang_id));
    $param['pdate'] = htmlspecialchars(trim($_POST["pdate"][$__lang_id]));
    $param['subj'] = htmlspecialchars(trim($_POST["subj"][$__lang_id]));
	$param['content'] = trim($_POST["content"][$__lang_id]);
    $param['status'] = '1';
    $param['creator'] = $loginAccount->account_id;
    $oNews->setAdd($param);	
    $m = $oNews->add();
    if($m != ""){
    	$msg .= "[".$langAry['lang_name']."] ". $m ."\r\n";
    }
}

if ($msg != "") {
    // echo "<script>alert('".$msg."');history.back();</script>";
    echo $msg;
} else {
    // echo "<script>alert('新增完成');history.go(-2);</script>";
    // echo "<script>alert('新增完成');location.href  = 'NewsList.php';</script>";
    echo 'ok';
}
exit();
