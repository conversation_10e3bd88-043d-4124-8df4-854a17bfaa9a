<?php

//ini_set('display_errors','on') ;
require_once($_SERVER["DOCUMENT_ROOT"]."/resonac-all/sysconfig/config.php");
require_once(LIBRARY_DIR."library/classes/admin/Control.php");
require_once(LIBRARY_DIR."library/classes/admin/Account.php");
require_once(LIBRARY_DIR."library/classes/admin/News.php");
Authenticator::isAccountLogin("ss_account");

$seqno = trim($_GET["seqno"]);

$oDB = new DB(DB_HOST, DB_NAME, DB_USER, DB_PASSWORD, true);
if (!$oDB->open()) {
    die(CONNECTION_DB_FAILED.$oDB->error());
}

$oNews = new News($oDB);
$oNews->getFromDb($seqno);
$msg = $oNews->del();
if ($msg != "") {
    // echo "<script>alert('".$msg."');history.back();</script>";
    echo $msg;
} else {
    // echo "<script>alert('刪除完成');history.back();</script>";
    // echo "<script>alert('刪除完成');location.href  = 'NewsList.php';</script>";
    echo 'ok';
}
exit();
