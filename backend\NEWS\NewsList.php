<?php
//ini_set('display_errors','on');
ini_set('display_errors','off');
require_once($_SERVER["DOCUMENT_ROOT"]."/resonac-all/sysconfig/config.php");
require_once(LIBRARY_DIR."library/classes/admin/Control.php");
require_once(LIBRARY_DIR."library/classes/admin/Account.php");
require_once(LIBRARY_DIR."library/classes/admin/Language.php");
require_once(LIBRARY_DIR."library/classes/admin/News.php");
Authenticator::isAccountLogin("ss_account");

$oDB = new DB(DB_HOST, DB_NAME, DB_USER, DB_PASSWORD, true);
if (!$oDB->open()) {
  die(CONNECTION_DB_FAILED.$oDB->error());
}

$lang_id = substr(trim($_REQUEST["lang_id"]),0,5);
$oLanguage = new Language();
$lang_id = $oLanguage->getLang($lang_id); 
$LanguageAry = $oLanguage->Load_Language($lang_id, 'array');

$page = trim($_POST["page"]);
$oNewsList = new NewsList($oDB);
$oNewsList->getNewsList("",$lang_id);

$oNewsList->setPageSize(20) ;
$page = intval($page) ;
$page = ($page < 1) ? 1 : $page ;
$page = ($page == ENDPAGE || $page > $oNewsList->totalPage) ? $oNewsList->totalPage : $page ;
$oNewsList->getPage($page);
$show_color = ($lang_id=='1')?'green':'gray';
?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>台灣力森諾科特殊氣體股份有限公司-管理平台</title>
<style type="text/css">
  body {
    margin-left: 0px;
    margin-top: 0px;
    margin-right: 0px;
    margin-bottom: 0px;
    background-image: url();
  }
  .style1 {
    font-size: 12px;
    font-family: Verdana, Arial, Helvetica, sans-serif;
    font-weight: normal;
    color: #000000;
  }
  .style2 {
    color: #FFFFFF;
    font-family: Verdana, Arial, Helvetica, sans-serif;
    font-size: 12px;
    font-weight: normal;
  }
  .style2 a:link {
    color: #FFFFFF;
  }
  .style2 a:visited {
    color: #FFFFFF;
  }
  .style2 a:hover {
    color: #FD3904;
  }
  .style2 a:active {
    color: #FD3904;
  }
  .style1 a:link {
    color: #006600;
  }
  .style1 a:visited {
    color: #FD3904;
  }
  .style1 a:hover {
    color: #000000;
  }
  .style1 a:active {
    color: #000000;
  }
  /* 中文版顯示以下樣式 */
  table.green {
    border: 1px solid #00a5c0;
  }
  table.green tr:first-child{
    background: #00a5c0;
  }
  table.green .style1 a:link {
    color: #00a5c0;
  }
  /* 英文版顯示以下樣式 */
  table.gray {
    border: 1px solid #526366;
  }
  table.gray tr:first-child{
    background: #526366;
  }
  table.gray .style1 a:link {
    color: #526366;
  }
</style>
<script type="text/javascript">
  function MM_preloadImages() { //v3.0
    var d=document; if(d.images){ if(!d.MM_p) d.MM_p=new Array();
    var i,j=d.MM_p.length,a=MM_preloadImages.arguments; for(i=0; i<a.length; i++)
    if (a[i].indexOf("#")!=0){ d.MM_p[j]=new Image; d.MM_p[j++].src=a[i];}}
  }
  function MM_swapImgRestore() { //v3.0
    var i,x,a=document.MM_sr; for(i=0;a&&i<a.length&&(x=a[i])&&x.oSrc;i++) x.src=x.oSrc;
  }
  function MM_findObj(n, d) { //v4.01
    var p,i,x;  if(!d) d=document; if((p=n.indexOf("?"))>0&&parent.frames.length) {
    d=parent.frames[n.substring(p+1)].document; n=n.substring(0,p);}
    if(!(x=d[n])&&d.all) x=d.all[n]; for (i=0;!x&&i<d.forms.length;i++) x=d.forms[i][n];
    for(i=0;!x&&d.layers&&i<d.layers.length;i++) x=MM_findObj(n,d.layers[i].document);
    if(!x && d.getElementById) x=d.getElementById(n); return x;
  }
  function MM_swapImage() { //v3.0
    var i,j=0,x,a=MM_swapImage.arguments; document.MM_sr=new Array; for(i=0;i<(a.length-2);i+=3)
    if ((x=MM_findObj(a[i]))!=null){document.MM_sr[j++]=x; if(!x.oSrc) x.oSrc=x.src; x.src=a[i+2];}
  }
  function goPage(page) {
    f = window.document.form1;
    f.page.value = page ;	
    f.submit() ;
  }
  function goToUrl(url, langID) {
    f = window.document.form1;
    f.action = url;
    if(langID != ""){
      f.lang_id.value = langID;
    }
    f.submit();
  }
  function goDel(url, langID) {
    if (confirm("<?php echo '您確定要刪除資料嗎？';?>")){
  	  // location.href = url;
      $.ajax({
          type: "GET",
          url: url,
          data: new FormData($('[name="form1"]')[0]),
          contentType: false,
          processData: false,
          success: function (data) {
            if (data == 'ok'){
              alert('刪除完成');
              window.location.reload();
            } else {
              alert(data);
            }
          }
      });
    }
  }
</script>
</head>

<body onload="MM_preloadImages('../images/control_bt02_01.gif','../images/control_bt02_02.gif','../images/control_bt_02.gif')">
  <table width="960" border="0" align="center" cellpadding="0" cellspacing="0">
    <tr>
      <th colspan="2" scope="row"><img src="../images/control_01.gif" width="960" height="109" /></th>
    </tr>
    <tr>
      <th width="150" valign="top" bgcolor="#FFFFFF" scope="row">
        <table width="140" border="0" cellspacing="0" cellpadding="0">
    <?php include ("../left.php");
          include("common.php"); ?>
        </table>
      </th>
      <td width="810" valign="top" bgcolor="#FFFFFF">
        <table width="810" border="0" cellspacing="0" cellpadding="0">
          <div class="LanguageDv">
            <ul class="LanguageBtnList" style="float:left;">
      <?php for($i = 0; $i < count($LanguageAry); $i++){
              $actived = ($LanguageAry[$i]['selected'] != "") ? "active" : ""; ?>
              <li>
                <a href="#" onclick="goToUrl('NewsList.php', '<?php echo $LanguageAry[$i]['lang_id'];?>');return false;" >
                  <div class="btn_Language <?php echo $actived;?>">
                    <?php echo $LanguageAry[$i]['lang_name'];?>
                  </div>
                </a>
              </li>
      <?php } ?>
            </ul>
            <h2>【按鈕為黑色是當前語系】</h2>
          </div>
          <!-- <div class="content">
      <?php //foreach($LanguageAry as $key=>$langAry){
	    	      //$__lang_id = $langAry['lang_id'];
	    	      //$__display = ($langAry['selected'] != "") ? "" : "display:none;"; ?>
	            <div class="LanguageInputArea" id="LanguageInputArea<?php echo $__lang_id;?>" style="<?php echo $__display;?>" > -->
                <tr>
                  <th height="50" align="left" scope="row"><span class="style1">【最新消息資料】</span></th>
                </tr>
                <tr>
                  <th align="left" valign="top" scope="row"><label></label>
                    <table width="800" border="0" cellspacing="0" cellpadding="0">
                      <form name="form1" action="" method="post">
	                      <input type="hidden" name="lang_id" value="<?php echo $lang_id;?>" /> 
                        <tr>
                          <td width="600" align="left" scope="row"></td>
                          <td width="200" class="style1">
                      <?php if ($page <= 1){?>Prev.<?php }else{?><a href="javascript:goPage('<?php echo $page-1?>')">Prev.</a><?php }?>|
                      <?php if ($page >= $oNewsList->totalPage){?>Next<?php }else{?><a href="javascript:goPage('<?php echo $page+1?>')">Next</a><?php }?>第
                              <select name="page" size="1" onChange="goPage(this.options[this.selectedIndex].value)">
                          <?php for ($a=1; $a <= $oNewsList->totalPage;$a++){ ?>
                                  <option value="<?php echo $a?>" <?php echo ($a == $page) ? "selected" : "";?>><?php echo $a?></option>
                          <?php }?>
                              </select>頁 / <?php echo $oNewsList->size()?> 筆
                          </td>
                        </tr>
                      </form>
                    </table>
                  </th>
                </tr>
                <tr>
                  <th align="center" scope="row">
                    <table width="780" border="1" align="center" cellpadding="0" cellspacing="1" class="<?php echo $show_color;?>">
                      <tr>
                        <td height="30" align="center">
                          <span class="style2">序號</span>
                        </td>
                        <td height="30" align="center" scope="row">
                          <span class="style2">狀態</span>
                        </td>
                        <td height="30" align="center">
                          <span class="style2">公告日期</span>
                        </td>
                        <td height="30" align="center">
                          <span class="style2">標題</span>
                        </td>
                        <td height="30" align="center">
                          <span class="style2">
                            <input type="button" name="but" value="新增" onclick="goToUrl('NewsAdd.php', '');return false;">
                          </span>
                        </td>
                      </tr>
                <?php for($i=$oNewsList->getInPageStart();$i < $oNewsList->getInPageEnd();$i++) {
                        $ne = $oNewsList->get($i);
                        $j = $i+1; ?>
                        <!-- <tr onmouseover="overcolor(this)" onmouseout="outcolor(this)"> -->
                        <tr>
                          <td height="30" width="5%" align="center" class="style1"><?php echo $j;?></td>
                          <td height="30" width="5%" align="center" class="style1"><?php echo $ne->getStatusName();?></td>
                          <td height="30" width="10%" align="center" class="style1"><?php echo $ne->getPdate();?></td>
                          <td height="30" width="40%" align="center" class="style1"><?php echo $ne->getSubj();?></td>
                          <td height="30" width="10%" align="center" class="style1">
                            <a href="#" onClick="goToUrl('NewsMod.php?seqno=<?php echo $ne->getSeqno();?>', '<?php echo $ne->getLang_id()?>');return false;">修改</a>|
                            <a href="#" onClick="goDel('NewsDel.php?seqno=<?php echo $ne->getSeqno();?>', '<?php echo $ne->getLang_id()?>');return false;">刪除</a>
                          </td>
                        </tr>
                <?php }
                      $oNewsList->clear();?>
                    </table>
                  </th>
                </tr>
              <!-- </div>
      <?php //} ?>
          </div> -->
        </table>
      </td>
    </tr>
  </table>
</body>
</html>