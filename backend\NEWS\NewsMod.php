<?php
//ini_set('display_errors','on');
require_once($_SERVER["DOCUMENT_ROOT"]."/resonac-all/sysconfig/config.php");
require_once(LIBRARY_DIR."library/classes/admin/Control.php");
require_once(LIBRARY_DIR."library/classes/admin/Account.php");
require_once(LIBRARY_DIR."library/classes/admin/Language.php");
require_once(LIBRARY_DIR."library/classes/admin/News.php");
Authenticator::isAccountLogin("ss_account");

$oDB = new DB(DB_HOST, DB_NAME, DB_USER, DB_PASSWORD, true);
if (!$oDB->open()) {
  die(CONNECTION_DB_FAILED.$oDB->error());
}

$seqno = trim($_GET["seqno"]);
$oNews = new News($oDB);
$oNews->getFromDb($seqno);

$common_id = $oNews->getcommon_id();
$lang_id = substr(trim($_POST["lang_id"]),0,5);
$oLanguage = new Language();
$lang_id = $oLanguage->getLang($lang_id); 
$LanguageAry = $oLanguage->Load_Language($lang_id, 'array');
?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>台灣力森諾科特殊氣體股份有限公司-管理平台</title>
<style type="text/css">
body {
	margin-left: 0px;
	margin-top: 0px;
	margin-right: 0px;
	margin-bottom: 0px;
	background-image: url();
}
.style1 {
	font-size: 12px;
	font-family: Verdana, Arial, Helvetica, sans-serif;
	font-weight: normal;
	color: #000000;
}
.style2 {
	color: #FFFFFF;
	font-family: Verdana, Arial, Helvetica, sans-serif;
	font-size: 12px;
	font-weight: normal;
}
.style2 a:link {
	color: #FFFFFF;
}
.style2 a:visited {
	color: #FFFFFF;
}
.style2 a:hover {
	color: #FD3904;
}
.style2 a:active {
	color: #FD3904;
}
.style1 a:link {
	color: #006600;
}
.style1 a:visited {
	color: #FD3904;
}
.style1 a:hover {
	color: #000000;
}
.style1 a:active {
	color: #000000;
}
/* 中文版顯示以下樣式 */
table.green {
  border: 1px solid #00a5c0;
}
table.green tr th:first-child{
  background: #00a5c0;
}
/* 英文版顯示以下樣式 */
table.gray {
  border: 1px solid #526366;
}
table.gray tr th:first-child{
  background: #526366;
}
</style>
<script type="text/javascript">
  function MM_preloadImages() { //v3.0
    var d=document; if(d.images){ if(!d.MM_p) d.MM_p=new Array();
    var i,j=d.MM_p.length,a=MM_preloadImages.arguments; for(i=0; i<a.length; i++)
    if (a[i].indexOf("#")!=0){ d.MM_p[j]=new Image; d.MM_p[j++].src=a[i];}}
  }
  function MM_swapImgRestore() { //v3.0
    var i,x,a=document.MM_sr; for(i=0;a&&i<a.length&&(x=a[i])&&x.oSrc;i++) x.src=x.oSrc;
  }
  function MM_findObj(n, d) { //v4.01
    var p,i,x;  if(!d) d=document; if((p=n.indexOf("?"))>0&&parent.frames.length) {
    d=parent.frames[n.substring(p+1)].document; n=n.substring(0,p);}
    if(!(x=d[n])&&d.all) x=d.all[n]; for (i=0;!x&&i<d.forms.length;i++) x=d.forms[i][n];
    for(i=0;!x&&d.layers&&i<d.layers.length;i++) x=MM_findObj(n,d.layers[i].document);
    if(!x && d.getElementById) x=d.getElementById(n); return x;
  }
  function MM_swapImage() { //v3.0
    var i,j=0,x,a=MM_swapImage.arguments; document.MM_sr=new Array; for(i=0;i<(a.length-2);i+=3)
    if ((x=MM_findObj(a[i]))!=null){document.MM_sr[j++]=x; if(!x.oSrc) x.oSrc=x.src; x.src=a[i+2];}
  }
  function isValidDate(dateString) {
    if (!/^\d{4}-\d{2}-\d{2}$/.test(dateString)) return false;
    var parts = dateString.split("-");
    var year = parseInt(parts[0], 10);
    var month = parseInt(parts[1], 10);
    var day = parseInt(parts[2], 10);
    var date = new Date(year, month - 1, day);
    if (date.getMonth() + 1 !== month || date.getDate() !== day) {
      return false;
    }
    return true;
  }
  function check_data() {
    var langID = 0;
    var langName = "";
    var isOK = true;
    var lang_id_index = null; // 目前語言 index
    var lang_id_val = null; // 目前語言 名稱
    var langAry = new Array(); // 語言 陣列， key => 語言 index, value=> 語言名稱

    if($(".LanguageBtnList li a div.active").length <= 0 || $("input[name='Language[]']").length <= 0){
      alert("【請選擇語言!】");
      return false;
    }
    $("input[name='Language[]']").each(function(index) {
      if($(".btn_Language").eq(index).hasClass( "active" )){
        lang_id_index = index;
      }
      langAry.push($( this ).val());
    });
    for (var i=0; i < langAry.length; i++) {
      langID = langAry[i];
      langName = $(".btn_Language").eq(i).text().trim();
      var pdate_selector = $('input[name="pdate['+langID+']"]');
      var subj_selector = $('input[name="subj['+langID+']"]');
      var content_selector = $('textarea[name="content['+langID+']"]');
      if(pdate_selector.length <= 0){
        continue;
      }
      if(pdate_selector.val() == ""){
        isOK = false;
        alert("請輸入【"+ langName +"】【公告日期!】");
        if(lang_id_index == i){
          pdate_selector.focus();
        }
        break;
      }else if(!isValidDate(pdate_selector.val())){
        isOK = false;
        alert("【"+ langName +"】【公告日期格式錯誤!】");
      }
      if(subj_selector.val() == ""){
        isOK = false;
        alert("請輸入【"+ langName +"】【標題!】");
        if(lang_id_index == i){
          subj_selector.focus();
        }
        break;
      }
      if(content_selector.val() == ""){
        isOK = false;
        alert("請輸入【"+ langName +"】【內容!】");
        if(lang_id_index == i){
          content_selector.focus();
        }
        break;
      }
    }
    if(!isOK){
      return false;
    }
    $.ajax({
        type: "POST",
        url: "NewsModEx.php",
        data: new FormData($('[name="form1"]')[0]),
        contentType: false,
        processData: false,
        success: function (data) {
          if (data == 'ok'){
            alert('修改完成');
            location.href  = 'NewsList.php';
          } else {
            alert(data);
          }
        }
    });
    // if (document.form1.pdate.value=="") {
    //   alert("請輸入公告日期");
    //   return false;
    // } else if (document.form1.subj.value=="") {
    //   alert("請輸入標題");
    //   return false;
    // } else if (document.form1.content.value=="") {
    //   alert("請輸入內容");
    //   return false;
    // } else {
    //   return true;
    // }
  }
</script>
</head>

<body onload="MM_preloadImages('../images/control_bt02_01.gif','../images/control_bt02_02.gif','../images/control_bt_02.gif')">
  <table width="960" border="0" align="center" cellpadding="0" cellspacing="0">
    <tr>
      <th colspan="2" scope="row"><img src="../images/control_01.gif" width="960" height="109" /></th>
    </tr>
    <tr>
      <th width="150" valign="top" bgcolor="#FFFFFF" scope="row">
        <table width="140" border="0" cellspacing="0" cellpadding="0">
    <?php include ("../left.php");
          include("common.php"); ?>
        </table>
      </th>
      <td width="810" valign="top" bgcolor="#FFFFFF">
        <div class="LanguageDv">
        	<ul class="LanguageBtnList" style="float:left;">
    <?php for($i = 0; $i < count($LanguageAry); $i++){
            $actived = ($LanguageAry[$i]['selected'] != "") ? "active" : ""; ?>
        		<li>
        			<a href="#" onclick="switchLanguage(this, '<?php echo $LanguageAry[$i]['lang_id'];?>');return false;" >
        				<div class="btn_Language <?php echo $actived;?>">
        					<?php echo $LanguageAry[$i]['lang_name'];?>
        				</div>
        			</a>
        			<input TYPE="hidden" name="Language[]" value="<?php echo $LanguageAry[$i]['lang_id'] ;?>">
        		</li>
    <?php } ?>
      	  </ul>
          <h2>【按鈕為黑色是當前語系】</h2>
        </div>
        <div class="content">
          <form name="form1" action="NewsModEx.php" method="post" enctype="multipart/form-data">
      <?php foreach($LanguageAry as $key=>$langAry){
              $__lang_id = $langAry['lang_id'];
              $__display = ($langAry['selected'] != "") ? "" : "display:none;";
              $show_color = ($__lang_id=='1')?'green':'gray';
              $val = array("seqno"=>"", "status"=>"1", "pdate"=>"", "subj"=>"", "content"=>"", "img1"=>"", "img2"=>"", "img3"=>"", "img4"=>"", "img5"=>"", "img6"=>"", "img7"=>"", "img8"=>"", "img9"=>"", "img10"=>"");
			        if($oNews->getFromDbByLangID($common_id, $__lang_id)){
			        	$val["seqno"] = $oNews->getSeqno();
			        	$val["status"] = $oNews->getStatus();
			        	$val["pdate"] = $oNews->getPdate();
			        	$val["subj"] = $oNews->getSubj();
			        	$val["content"] = $oNews->getContent();
                $oNews->setImgUnit();
                for($y=0;$y < 10;$y++){
                  $val["img".($y+1)] = $oNews->getImgTagB($y);
                }
			        } ?>
              <div class="LanguageInputArea" id="LanguageInputArea<?php echo $__lang_id;?>" style="<?php echo $__display;?>" >
                <table width="810" border="0" cellspacing="0" cellpadding="0">
                  <input type="hidden" name="seqno[<?PHP echo $__lang_id;?>]" value="<?php echo $val["seqno"];?>" /> 
			            <input type="hidden" name="common_id[<?PHP echo $__lang_id;?>]" value="<?php echo $common_id;?>" /> 
                  <tr>
                    <th height="50" align="left" scope="row">
                      <span class="style1">【最新消息修改】<font color="red">*欄位為必填</font></span>
                    </th>
                  </tr>
                  <tr>
                    <th align="center" scope="row">
                      <table width="780" border="1" align="center" cellpadding="0" cellspacing="1" class="<?php echo $show_color;?>">
                        <tr>
                          <th height="30" align="center" scope="row">
                            <span class="style2">狀態</span>
                          </th>
                          <td height="30" align="left" class="style1">
                            <select name="status[<?PHP echo $__lang_id;?>]">
                              <option value="1" <?php echo($val["status"]=="1" ? "selected" : "");?>>開啟</option>
                              <option value="0" <?php echo($val["status"]=="0" ? "selected" : "");?>>關閉</option>
                            </select>
                          </td>
                        </tr>
                        <tr>
                          <th height="30" align="center" scope="row">
                            <span class="style2"><font color="red">*</font>公告日期</span>
                          </th>
                          <td height="30" align="left" class="style1">
                            <input type="text" name="pdate[<?PHP echo $__lang_id;?>]" size="10" length="10" value="<?php echo $val["pdate"];?>">(ex:2011-01-01)
                          </td>
                        </tr>
                        <tr>
                          <th height="30" align="center" scope="row">
                            <span class="style2"><font color="red">*</font>標題</span>
                          </th>
                          <td height="30" align="left" class="style1">
                            <input type="text" name="subj[<?PHP echo $__lang_id;?>]" size="40" value="<?php echo $val["subj"];?>">
                          </td>
                        </tr>
                        <tr>
                          <th height="30" align="center" scope="row">
                            <span class="style2"><font color="red">*</font>內容</span>
                          </th>
                          <td height="30" align="left" class="style1">
                            <textarea name="content[<?PHP echo $__lang_id;?>]" rows="10" cols="50"><?php echo $val["content"];?></textarea>
                          </td>
                        </tr>
                  <?php for($i=0;$i < 10;$i++) {
                          $k=$i+1;?>
                          <tr>
                            <th height="30" align="center" scope="row">
                              <span class="style2">圖片<?php echo $k;?></span>
                            </th>
                            <td height="30" align="left" class="style1">
                              <input type="file" name="userfile[<?PHP echo $__lang_id;?>][]" size="30">(圖檔格式為gif或jpg)<br>
                        <?php if($val["img".$k] !="") {
                                echo $val["img".$k];?>
                                <input type="checkbox" name="del<?php echo $k;?>[<?PHP echo $__lang_id;?>]" value="1">刪除
                        <?php }?>
                            </td>
                          </tr>
                  <?php }?>
                      </table>
                    </th>
                  </tr>
                </table>
              </div>
      <?php } ?>
            <tr>
              <td height="30" align="center" class="style1" colspan="2">
                <input type="button" name="sub" value="送出" onclick="check_data();"><input type="reset" name="res" value="重填">
              </td>
            </tr>
          </form>
        </div>
      </td>
    </tr>
  </table>
</body>
</html>