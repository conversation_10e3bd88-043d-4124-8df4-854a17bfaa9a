<?php
// ini_set('display_errors','on');
require_once($_SERVER["DOCUMENT_ROOT"]."/resonac-all/sysconfig/config.php");
require_once(LIBRARY_DIR."library/classes/admin/Control.php");
require_once(LIBRARY_DIR."library/classes/admin/Account.php");
require_once(LIBRARY_DIR."library/classes/admin/Language.php");
require_once(LIBRARY_DIR."library/classes/admin/News.php");

Authenticator::isAccountLogin("ss_account");

$oDB = new DB(DB_HOST, DB_NAME, DB_USER, DB_PASSWORD, true);
if (!$oDB->open()) {
    die(CONNECTION_DB_FAILED.$oDB->error());
}

$oNews = new News($oDB);
$oLanguage = new Language();
$LanguageAry = $oLanguage->Load_Language("", 'array');

$msg = "";
foreach($LanguageAry as $key=>$langAry){
	$__lang_id = $langAry['lang_id'];
	$param = array();
    $seqno = trim($_POST["seqno"][$__lang_id]);
	$common_id = htmlspecialchars(trim($_POST["common_id"][$__lang_id]));
    $param['pdate'] = trim($_POST["pdate"][$__lang_id]);
    $param['subj'] = trim($_POST["subj"][$__lang_id]);
    $param['content'] = trim($_POST["content"][$__lang_id]);
    $param['status'] = trim($_POST["status"][$__lang_id]);
    // 新增
    if($seqno == ""){
        $param['lang_id'] = htmlspecialchars(trim($__lang_id));
        $param['common_id'] = $common_id;
        $param['creator'] = $loginAccount->account_id;
        $oNews->setAdd($param);	
        $m = $oNews->add();
    // 修改
    }else{
        if($oNews->getFromDb($seqno)){
            $param['del1'] = trim($_POST["del1"][$__lang_id]);
            $param['del2'] = trim($_POST["del2"][$__lang_id]);
            $param['del3'] = trim($_POST["del3"][$__lang_id]);
            $param['del4'] = trim($_POST["del4"][$__lang_id]);
            $param['del5'] = trim($_POST["del5"][$__lang_id]);
            $param['del6'] = trim($_POST["del6"][$__lang_id]);
            $param['del7'] = trim($_POST["del7"][$__lang_id]);
            $param['del8'] = trim($_POST["del8"][$__lang_id]);
            $param['del9'] = trim($_POST["del9"][$__lang_id]);
            $param['del10'] = trim($_POST["del10"][$__lang_id]);
            $param['lang_id'] = $oNews->getLang_id();
            $param['common_id'] = $oNews->getCommon_id();
            if($param['lang_id'] != $__lang_id || $param['common_id'] != $common_id){
                $m = "(修改) 語系及編號錯誤(".$param['lang_id']."/".$__lang_id." , ".$param['common_id']."/".$common_id.")！！";
            }else{
                $oNews->setUpdate($param);	
                $m = $oNews->update();
            }
        }else{
            $m = "(修改) 編號錯誤(".$seqno.")！！";
        }
    }
    if($m != ""){
        $msg .= "[".$langAry['lang_name']."] ". $m ."\r\n";
    }
}

if ($msg != "") {
    // echo "<script>alert('".$msg."');history.back();</script>";
    echo $msg;
} else {
    // echo "<script>alert('修改完成');history.go(-2);</script>";
    // echo "<script>alert('修改完成');location.href  = 'NewsList.php';</script>";
    echo 'ok';
}
exit();
