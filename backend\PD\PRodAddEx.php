<?php

//ini_set('display_errors','on') ;
require_once($_SERVER["DOCUMENT_ROOT"]."/resonac-all/sysconfig/config.php");
require_once(LIBRARY_DIR."library/classes/admin/Control.php");
require_once(LIBRARY_DIR."library/classes/admin/Account.php");
require_once(LIBRARY_DIR."library/classes/admin/Product.php");
Authenticator::isAccountLogin("ss_account");

$name 		= trim($_POST["name"]);
$place      = trim($_POST["place"]);
$content = trim($_POST["content"]);
$url_link = trim($_POST["url_link"]);
$place = trim($_POST["place"]);
$pm_seq = trim($_POST["pm_seq"]);

$oDB = new DB(DB_HOST, DB_NAME, DB_USER, DB_PASSWORD, true);
if (!$oDB->open()) {
    die(CONNECTION_DB_FAILED.$oDB->error());
}

$oPRod = new Product($oDB);
$oPRod->setAdd($name, $content, $url_link, $place, "1", $loginAccount->account_id, $pm_seq);
$msg = $oPRod->add();
if ($msg != "") {
    echo "<script>alert('".$msg."');history.back();</script>";
} else {
    echo "<script>alert('新增完成');history.go(-2);</script>";
}
