<?php
//ini_set('display_errors','on') ;
require_once($_SERVER["DOCUMENT_ROOT"]."/resonac-all/sysconfig/config.php");
require_once(LIBRARY_DIR."library/classes/admin/Control.php");
require_once(LIBRARY_DIR."library/classes/admin/Account.php");
require_once(LIBRARY_DIR."library/classes/admin/Product.php");
Authenticator::isAccountLogin("ss_account");

$oDB = new DB(DB_HOST, DB_NAME, DB_USER, DB_PASSWORD, true);
if (!$oDB->open()) {
    die(CONNECTION_DB_FAILED.$oDB->error());
}

$seqno = trim($_GET["seqno"]);
$oPMainList = new PMainList($oDB);
$oPMainList->getPMainList("1");

$oPRod = new Product($oDB);
$oPRod->getFromDb($seqno);
$oPRod->setImgUnit();
?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>台灣力森諾科特殊氣體股份有限公司-管理平台</title>
<style type="text/css">
<!--
body {
	margin-left: 0px;
	margin-top: 0px;
	margin-right: 0px;
	margin-bottom: 0px;
	background-image: url();
}
.style1 {
	font-size: 12px;
	font-family: Verdana, Arial, Helvetica, sans-serif;
	font-weight: normal;
	color: #000000;
}
.style2 {
	color: #FFFFFF;
	font-family: Verdana, Arial, Helvetica, sans-serif;
	font-size: 12px;
	font-weight: normal;
}
.style2 a:link {
	color: #FFFFFF;
}
.style2 a:visited {
	color: #FFFFFF;
}
.style2 a:hover {
	color: #FD3904;
}
.style2 a:active {
	color: #FD3904;
}
.style1 a:link {
	color: #006600;
}
.style1 a:visited {
	color: #FD3904;
}
.style1 a:hover {
	color: #000000;
}
.style1 a:active {
	color: #000000;
}
-->
</style>
<script type="text/javascript">
<!--
function MM_preloadImages() { //v3.0
  var d=document; if(d.images){ if(!d.MM_p) d.MM_p=new Array();
    var i,j=d.MM_p.length,a=MM_preloadImages.arguments; for(i=0; i<a.length; i++)
    if (a[i].indexOf("#")!=0){ d.MM_p[j]=new Image; d.MM_p[j++].src=a[i];}}
}
function MM_swapImgRestore() { //v3.0
  var i,x,a=document.MM_sr; for(i=0;a&&i<a.length&&(x=a[i])&&x.oSrc;i++) x.src=x.oSrc;
}
function MM_findObj(n, d) { //v4.01
  var p,i,x;  if(!d) d=document; if((p=n.indexOf("?"))>0&&parent.frames.length) {
    d=parent.frames[n.substring(p+1)].document; n=n.substring(0,p);}
  if(!(x=d[n])&&d.all) x=d.all[n]; for (i=0;!x&&i<d.forms.length;i++) x=d.forms[i][n];
  for(i=0;!x&&d.layers&&i<d.layers.length;i++) x=MM_findObj(n,d.layers[i].document);
  if(!x && d.getElementById) x=d.getElementById(n); return x;
}

function MM_swapImage() { //v3.0
  var i,j=0,x,a=MM_swapImage.arguments; document.MM_sr=new Array; for(i=0;i<(a.length-2);i+=3)
   if ((x=MM_findObj(a[i]))!=null){document.MM_sr[j++]=x; if(!x.oSrc) x.oSrc=x.src; x.src=a[i+2];}
}
//-->
</script>
<script>
function check_data() {
	if (document.form1.name.value=="") {
		alert("請輸入產品名稱");
		return false;
	} else {
		return true;
	}
}
</script>
</head>

<body onload="MM_preloadImages('../images/control_bt02_01.gif','../images/control_bt02_02.gif','../images/control_bt_02.gif')">
<table width="960" border="0" align="center" cellpadding="0" cellspacing="0">
  <tr>
    <th colspan="2" scope="row"><img src="../images/control_01.gif" width="960" height="109" /></th>
  </tr>
  <tr>
    <th width="150" valign="top" bgcolor="#FFFFFF" scope="row"><table width="140" border="0" cellspacing="0" cellpadding="0">
      <!--<tr>
        <th scope="row"><table width="130" border="1" align="center" cellpadding="0" cellspacing="1" bordercolor="#FFFFFF">
          <tr>
            <th colspan="4" bordercolor="#007AD7" class="style1" scope="row"><table width="125" border="0" cellspacing="0" cellpadding="0">
              <tr>
                <th height="20" class="style1" scope="row">HI,管理者 您好</th>
              </tr>
              <tr>
                <th height="20" class="style1" scope="row"><a href="#" class="style1">登出</a></th>
              </tr>
            </table></th>
          </tr>
          
        </table></th>
      </tr>
      <tr>
        <th scope="row">&nbsp;</th>
      </tr>
      <tr>
        <th scope="row"><table width="125" height="58" border="0" align="center" cellpadding="0" cellspacing="0" id="___01">
          <tr>
            <td height="29"><a href="#" onmouseout="MM_swapImgRestore()" onmouseover="MM_swapImage('Image9','','../images/control_bt02_01.gif',1)"><img src="../images/control_bt01_01.gif" name="Image9" width="125" height="29" border="0" id="Image9" /></a></td>
          </tr>
          <tr>
            <td height="29"><a href="#" onmouseout="MM_swapImgRestore()" onmouseover="MM_swapImage('Image10','','../images/control_bt02_02.gif',1)"><img src="../images/control_bt01_02.gif" name="Image10" width="125" height="29" border="0" id="Image10" /></a></td>
          </tr>
        </table></th>
      </tr>!-->
      <?php include "../left.php";?>
    </table></th>
    <td width="810" valign="top" bgcolor="#FFFFFF"><table width="810" border="0" cellspacing="0" cellpadding="0">
	<form name="form1" action="PRodModEx.php" method="post" onsubmit="return check_data();" enctype="multipart/form-data">
	<input type="hidden" name="seqno" value="<?php echo $seqno;?>">
      <tr>
        <th height="50" align="left" scope="row"><span class="style1">【產品修改】<font color="red">*欄位為必填</font></span></th>
      </tr>
      <tr>
        <th align="center" scope="row"><table width="780" border="1" align="center" cellpadding="0" cellspacing="1" bordercolor="#017AD9">
          <tr>
            <th height="30" align="center" bordercolor="#017AD9" bgcolor="#017AD9" scope="row"><span class="style2">狀態</span></th>
            <td height="30" align="left" class="style1"><select name="status">
			<option value="1" <?php echo($oPRod->getStatus()=="1" ? "selected" : "");?>>開啟</option>
			<option value="0" <?php echo($oPRod->getStatus()=="0" ? "selected" : "");?>>關閉</option>
			</select></td>
          </tr>
		  <tr>
            <th height="30" align="center" bordercolor="#017AD9" bgcolor="#017AD9" scope="row"><span class="style2"><font color="red">*</font>產品類別</span></th>
            <td height="30" align="left" class="style1"><select name="pm_seq">
			<?php for($i=0;$i < $oPMainList->size();$i++) {
			    $pm = $oPMainList->get($i);
			    ?>
			<option value="<?php echo $pm->getSeqno();?>" <?php echo($pm->getSeqno()==$oPRod->getPMSeq() ? "selected" : "");?>><?php echo $pm->getName();?></option>
			<?php }$oPMainList->clear();?>
			</select></td>
          </tr>
		  <tr>
            <th height="30" align="center" bordercolor="#017AD9" bgcolor="#017AD9" scope="row"><span class="style2"><font color="red">*</font>產品名稱</span></th>
            <td height="30" align="left" class="style1"><input type="text" name="name" size="40" value="<?php echo $oPRod->getName();?>"></td>
          </tr>
		  <tr>
            <th height="30" align="center" bordercolor="#017AD9" bgcolor="#017AD9" scope="row"><span class="style2">產品內容</span></th>
            <td height="30" align="left" class="style1"><textarea name="content" rows="10" cols="30"><?php echo $oPRod->getContent();?></textarea></td>
          </tr>
		  <tr>
            <th height="30" align="center" bordercolor="#017AD9" bgcolor="#017AD9" scope="row"><span class="style2">產品圖片</span></th>
            <td height="30" align="left" class="style1"><input type="file" name="userfile[]" size="30">(圖檔格式為gif或jpg)<br>
			<?php if ($oPRod->getImgTagB() != "") {
			    echo $oPRod->getImgTagB();?><input type="checkbox" name="del1" value="1">刪除<?php }?>
			</td>
          </tr>
		  <tr>
            <th height="30" align="center" bordercolor="#017AD9" bgcolor="#017AD9" scope="row"><span class="style2">產品連結</span></th>
            <td height="30" align="left" class="style1"><input type="text" name="url_link" size="40" value="<?php echo $oPRod->getUrl();?>"></td>
          </tr>
		  <tr>
            <th height="30" align="center" bordercolor="#017AD9" bgcolor="#017AD9" scope="row"><span class="style2">位置順序</span></th>
            <td height="30" align="left" class="style1"><input type="text" name="place" size="3" value="<?php echo $oPRod->getPlace();?>"></td>
          </tr>
		  <tr>
            <td height="30" align="center" class="style1" colspan="2">
				<input type="submit" name="sub" value="送出"><input type="reset" name="res" value="重填">
			</td>
          </tr>
        </table></th>
      </tr>
    </form></table></td>
  </tr>
</table>
</body>
</html>