<?php

//ini_set('display_errors','on') ;
require_once($_SERVER["DOCUMENT_ROOT"]."/resonac-all/sysconfig/config.php");
require_once(LIBRARY_DIR."library/classes/admin/Control.php");
require_once(LIBRARY_DIR."library/classes/admin/Account.php");
require_once(LIBRARY_DIR."library/classes/admin/PMain.php");
Authenticator::isAccountLogin("ss_account");

$name 		= trim($_POST["name"]);
$place      = trim($_POST["place"]);

$oDB = new DB(DB_HOST, DB_NAME, DB_USER, DB_PASSWORD, true);
if (!$oDB->open()) {
    die(CONNECTION_DB_FAILED.$oDB->error());
}

$oPMain = new PMain($oDB);
$oPMain->setAdd($name, $place, "1", $loginAccount->account_id);
$msg = $oPMain->add();
if ($msg != "") {
    echo "<script>alert('".$msg."');history.back();</script>";
} else {
    echo "<script>alert('新增完成');history.go(-2);</script>";
}
