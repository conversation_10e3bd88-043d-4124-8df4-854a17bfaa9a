<?php
//ini_set('display_errors','on') ;
require_once($_SERVER["DOCUMENT_ROOT"]."/resonac-all/sysconfig/config.php");
require_once(LIBRARY_DIR."library/classes/admin/Control.php");
require_once(LIBRARY_DIR."library/classes/admin/Account.php");
require_once(LIBRARY_DIR."library/classes/admin/PMain.php");
Authenticator::isAccountLogin("ss_account");

$oDB = new DB(DB_HOST, DB_NAME, DB_USER, DB_PASSWORD, true);
if (!$oDB->open()) {
    die(CONNECTION_DB_FAILED.$oDB->error());
}

$oPMainList = new PMainList($oDB);
$oPMainList->getPMainList("");
?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>台灣力森諾科特殊氣體股份有限公司-管理平台</title>
<style type="text/css">
<!--
body {
	margin-left: 0px;
	margin-top: 0px;
	margin-right: 0px;
	margin-bottom: 0px;
	background-image: url();
}
.style1 {
	font-size: 12px;
	font-family: Verdana, Arial, Helvetica, sans-serif;
	font-weight: normal;
	color: #000000;
}
.style2 {
	color: #FFFFFF;
	font-family: Verdana, Arial, Helvetica, sans-serif;
	font-size: 12px;
	font-weight: normal;
}
.style2 a:link {
	color: #FFFFFF;
}
.style2 a:visited {
	color: #FFFFFF;
}
.style2 a:hover {
	color: #FD3904;
}
.style2 a:active {
	color: #FD3904;
}
.style1 a:link {
	color: #006600;
}
.style1 a:visited {
	color: #FD3904;
}
.style1 a:hover {
	color: #000000;
}
.style1 a:active {
	color: #000000;
}
-->
</style>
<script type="text/javascript">
<!--
function MM_preloadImages() { //v3.0
  var d=document; if(d.images){ if(!d.MM_p) d.MM_p=new Array();
    var i,j=d.MM_p.length,a=MM_preloadImages.arguments; for(i=0; i<a.length; i++)
    if (a[i].indexOf("#")!=0){ d.MM_p[j]=new Image; d.MM_p[j++].src=a[i];}}
}
function MM_swapImgRestore() { //v3.0
  var i,x,a=document.MM_sr; for(i=0;a&&i<a.length&&(x=a[i])&&x.oSrc;i++) x.src=x.oSrc;
}
function MM_findObj(n, d) { //v4.01
  var p,i,x;  if(!d) d=document; if((p=n.indexOf("?"))>0&&parent.frames.length) {
    d=parent.frames[n.substring(p+1)].document; n=n.substring(0,p);}
  if(!(x=d[n])&&d.all) x=d.all[n]; for (i=0;!x&&i<d.forms.length;i++) x=d.forms[i][n];
  for(i=0;!x&&d.layers&&i<d.layers.length;i++) x=MM_findObj(n,d.layers[i].document);
  if(!x && d.getElementById) x=d.getElementById(n); return x;
}

function MM_swapImage() { //v3.0
  var i,j=0,x,a=MM_swapImage.arguments; document.MM_sr=new Array; for(i=0;i<(a.length-2);i+=3)
   if ((x=MM_findObj(a[i]))!=null){document.MM_sr[j++]=x; if(!x.oSrc) x.oSrc=x.src; x.src=a[i+2];}
}
//-->
</script>
</head>

<body onload="MM_preloadImages('../images/control_bt02_01.gif','../images/control_bt02_02.gif','../images/control_bt_02.gif')">
<table width="960" border="0" align="center" cellpadding="0" cellspacing="0">
  <tr>
    <th colspan="2" scope="row"><img src="../images/control_01.gif" width="960" height="109" /></th>
  </tr>
  <tr>
    <th width="150" valign="top" bgcolor="#FFFFFF" scope="row"><table width="140" border="0" cellspacing="0" cellpadding="0">
      <?php include "../left.php";?>

    </table></th>
    <td width="810" valign="top" bgcolor="#FFFFFF"><table width="810" border="0" cellspacing="0" cellpadding="0">
      <tr>
        <th height="50" align="left" scope="row"><span class="style1">【產品類別資料】</span></th>
      </tr>
      <tr>
        <th align="center" scope="row"><table width="780" border="1" align="center" cellpadding="0" cellspacing="1" bordercolor="#017AD9">
          <tr>
			<td height="30" align="center" bgcolor="#017AD9"><span class="style2">序號</span></td>
            <th height="30" align="center" bordercolor="#017AD9" bgcolor="#017AD9" scope="row"><span class="style2">狀態</span></th>
            <td height="30" align="center" bgcolor="#017AD9"><span class="style2">類別名稱</span></td>
            <td height="30" align="center" bgcolor="#017AD9"><span class="style2">位置順序</span></td>
            <td height="30" align="center" bgcolor="#017AD9"><span class="style2"><input type="button" name="but" value="新增" onclick="location.href='PMainAdd.php';"></span></td>
          </tr>
		  <?php for($i=0;$i < $oPMainList->size();$i++) {
		      $pm = $oPMainList->get($i);
		      $j = $i+1;
		      ?>
          <tr onmouseover="overcolor(this)" onmouseout="outcolor(this)">
			<td height="30" align="center" class="style1"><?php echo $j;?></td>
            <td height="30" align="center" class="style1"><?php echo $pm->getStatusName();?></td>
            <td height="30" align="center" class="style1"><?php echo $pm->getName();?></td>
            <td height="30" align="center" class="style1"><?php echo $pm->getPlace();?></td>
            <td height="30" align="center" class="style1"><a href="PMainMod.php?seqno=<?php echo $pm->getSeqno();?>">修改</a>|<a href="PMainDel.php?seqno=<?php echo $pm->getSeqno();?>">刪除</a></td>
          </tr>
		  <?php }$oPMainList->clear();?>
        </table></th>
      </tr>
    </table></td>
  </tr>
</table>
</body>
</html>