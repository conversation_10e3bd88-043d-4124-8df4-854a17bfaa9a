<?php

//ini_set('display_errors','on') ;
require_once($_SERVER["DOCUMENT_ROOT"]."/resonac-all/sysconfig/config.php");
require_once(LIBRARY_DIR."library/classes/admin/Control.php");
require_once(LIBRARY_DIR."library/classes/admin/Account.php");
require_once(LIBRARY_DIR."library/classes/admin/PMain.php");
Authenticator::isAccountLogin("ss_account");

$name 		= trim($_POST["name"]);
$place      = trim($_POST["place"]);
$seqno = trim($_POST["seqno"]);
$status = trim($_POST["status"]);

$oDB = new DB(DB_HOST, DB_NAME, DB_USER, DB_PASSWORD, true);
if (!$oDB->open()) {
    die(CONNECTION_DB_FAILED.$oDB->error());
}

$oPMain = new PMain($oDB);
$oPMain->setUpdate($name, $status, $place, $seqno);
$msg = $oPMain->update();
if ($msg != "") {
    echo "<script>alert('".$msg."');history.back();</script>";
} else {
    echo "<script>alert('修改完成');history.go(-2);</script>";
}
