@charset "UTF-8";


html > body { 
   font-family: Arial, Helvetica, sans-serif,'微軟正黑體';
   background-color:#ffffff;
   background: no-repeat;
   font-size:12px;
} 

ol, ul {
   list-style:none;
   margin:0;
   padding:0;
}
table {
   border-collapse:collapse;
   border-spacing:0;
}


/* 標題 */
.title{
   color: #666;
   height: 40px;
   line-height: 40px;
   letter-spacing: 1px;
   margin:0 0 5px 0;
}
.title-left{
   float: left;
   font-size: 20px;
   font-weight: bolder;	
}
.title-right{
   float: right;
   font-size: 14px;
   color: #666;
   font-weight: bolder;
   background-color: rgb(255, 255, 153);
   margin:0 0 0 10px;
   padding:0 10px;
   
}
.title-right1{
   float: right;
   font-size: 15px;
   font-weight: bolder;
   margin-left: 15px;
}
   
.title-right > a{
   font-family: '微軟正黑體' ,Arial, Helvetica, sans-serif;
   font-size: 15px;
   text-decoration: none !important;
   color: #666;
}

.title-right:hover{
   text-decoration: none;
}


/****************** 工具*************************** */
.tool{
   overflow:hidden;
   /* border:#999 solid 1px; */
   border:#999 solid 0px;
   border-radius: 5px;
   background:#CCC;
   padding:10px 0 10px 0;
   
}
.tool table{
   font-size: 12px;
}
.tool-1 {
   margin:0 0 0 10px;	
}
.tool-1 li{
   float: left;	
   padding: 0 10px 0 0 ;
   line-height:1;
   line-height: 30px;
   
}
.pages{
   float: right;
   min-width:220px;
}
.pages li{
   float: left;
   padding: 0 0 0 10px;
   height:20px;
   line-height: 30px;
}
.pages li a ,.con_tab td a{
   color: #333;
   text-decoration: underline;
}
.pages li a:hover ,.con_tab td a:hover{
   text-decoration: none;	
}


/* *****************內容 ***************************/
.content{
   margin:5px 0 0 0 ;
}
.con_tab{
   text-align: center;
   font-size: 12px;
   border-collapse: separate;
   border-spacing: 5px;
}
.con_tab th{
   /* padding:10px;
   border:1px solid #999;
   background-color: #c8c8c8;
   color: #000;
   font-weight: bold;
   font-family: 微軟正黑體;
   font-size: 16px; */

   text-align: center;
   color: #FFFFFF;
   background: #c8c8c8;
   border-radius: 5px;
   border-bottom: 0;
   vertical-align: middle;
   padding: 0.5rem;
   border: 0;
   color: #000;
   font-weight: bold;
   font-family: 微軟正黑體;
   font-size: 16px;
}
.con_tab td{
   padding:10px;
   border:0px solid #999;
   font-size: 16px;
}

.con_tab2{
   text-align: center;
   font-size: 12px;
   border-collapse: separate;
   border-spacing: 5px;
}
.con_tab2 th{
   padding:10px;
   /* border:1px solid #999; */
   border:0px solid #999;
   border-radius: 5px;
   background-color: #c8c8c8;
   color: #000;
   font-weight: bold;
   font-family: 微軟正黑體;
   font-size: 16px;
}
.con_tab2 td{
   padding:10px;
   /* border:1px solid #999; */
   border:0px solid #999;
   font-size: 16px;
   word-break: break-all;
}


.but{
   margin:0 auto;
   display: block;
   width:45px;
   height:20px;
   line-height:20px;
   color:#444;
   background:#fff;
   border:#333 solid 1px;
   text-decoration: none;
}
.but:hover{
   color:#222;
   background:#CCC;
}
   
.td_9_18_FF0000 {
   color: #FF0000;
   font-family: Arial,Helvetica,sans-serif;
   font-size: 16px;
   line-height: 18px;
}

.tr_9_18_00_B{
   background-color:#C5D8ED;
}

.td_9_18_00_G{
   background-color:#CCCCCC;
}

.td_9_18_00_Y {
   background-color:#FFFF99;
}

.td_9_18_00_W {
   background-color:#FFFFFF;
}


/* 明細頁 table*/
.det_tab{
   font-family: Arial,Helvetica,sans-serif,'微軟正黑體';
   font-size: 12px;
   line-height: 18px;
}

/* table th*/
.det_tab th{
   color: #FFF;
   padding:15px 10px;
   background-color: #434343;
   border: #CCC solid 1px;
   
}

/* table td*/
.det_tab td{
   padding:15px 10px;
   border:#999 solid 1px;
}

/* 按鈕*/
.btn_88 {
   background-color: #F1F1F1;
   border: 1pt solid #888888;
   color: #666666;
   height: 18px;
   padding-top: 1px;
}

.btn_99 {
	background-color: #F1F1F1;
	border: 1pt solid #888888;
	color: #666666;
	height: 18px;
	padding-top: 1px;
 }

/* 2014.04.07 昆璋新增 藍字*/
.td_9_18_36c {
   color: #3366CC;
   font-family: Arial,Helvetica,sans-serif;
   font-size: 10pt;
   line-height: 18px;
}

.textfield_33 {
   background-color: #F1F1F1;
   border: 1pt solid #444444;
   color: #444444;
   font-family: Arial;
   height: 18px;
}
input[type="text"] ,input[type="password"]{
    /* height: 18px; */
    padding: 1px;
	margin:5px;
	font-size: 16px;
}
textarea{
	width:98%;
	margin:5px;
	height:50px;
}
.title , .tool , .content{
	/* width: 98%; */
	margin-right: 20px;
} 
select{
	margin:5px;
	height:24px;
	font-size: 16px;
}
input[type="checkbox"] , input[type="radio"]{
	margin :0 5px 0 20px;
}
.width_20{
	width:20% !important;
	height:20px;
}
.width_90{
	width:90% !important;
}
.picStyle , .picStyle_v{
	width:99%;
	min-height: 150px;
	background: #eee;
	color: #a9a9a9;
}
/*************************************menu***************************************/
.nav > li  {
	margin: 8px 0;
}
.nav > li > a{
	background-color: #0082DA;
	font-family: '微軟正黑體' ,Arial, Helvetica, sans-serif;
	color:#FFF;
	font-weight: bold;
	font-size: 18px;
}
.nav > li > a:hover{
	background-color: #000;
}
.nav > li > ul > li > a{
	background-color: #EEFFBB;
	font-size: 16px;
}

/**************************************list************************************/

.title-left1 {
	float:left;
}
.title-left1 a {
	font-family: '微軟正黑體' ,Arial, Helvetica, sans-serif;
	font-size:15px;
	text-decoration: none !important;
	color: #666;
}
.btn_div , .btn_div a{
	width:100%;
	color: #fff !important;
	text-decoration: none !important;
	font-family: '微軟正黑體' ,Arial, Helvetica, sans-serif;
	font-size:14px;
	text-align:center;
}
.search_btn_div{
	/* width:100px; */
	/* height:20px; */
}
.btn_clr_y{
	width:100px;
	background-color:#FFEE99;
}
.btn_clr_g{
	height:18px;
	margin:5px 15%;
	background-color:#99FF99;
}
.btn_clr_r{
	height:20px;
	margin:5px 15%;
	background-color:#cd2735;
}.btn_clr_r:hover{
	background-color:#eb2f3e;
}
.btn_clr_b {
	/* background-color:#C29C72; */
	background-color:#0082DA;
    border-radius: 15px;
    margin:5px 5%;
    width: 90px;
	height: 1em;
    float: left;
    font-weight: bold;
    line-height: 1em;
    padding: 5px 0;
	font-size: 16px;
}
.btn_clr_b:hover{
	background-color:#000;
}
.btn_float_left {
	float: left;
}
.tool {
	/* border: 1px solid #0d6fb8; */
	border: 0px solid #0d6fb8;
	border-radius: 5px;
	/* background-color: #BAC09A; */
	background-color: #7ecef4;
	font-size: 20px;
	font-family: 微軟正黑體;
	font-weight: bold;
}
.pages{
	height:30px;
	width:98%;
	float:left;
}
.search_ul{
	width:100%;
	/* color:#3366CC; */
	float:left;
	padding:0 10px;
	display:none;
}
.search_li{
	margin:5px 0 ;
}
.search_btn{
	width:110px;
	float:right;
	padding:0 10px;;
	display: none;
}
.sp_th{
	width: 25%;
	text-align: right;	
	display: inline-block;
	font-size:16px;
	font-family: 微軟正黑體;
	font-weight: bold;
}
.sp_td{		
	display: inline-block;
	font-size:16px;
}
#search_btn_li{
	height:30px;
	width:100%;
}
#btn_close{
	height:30px;
}
.btn_close_1{
	font-size:15px;
	float:left;
	margin:4px 0 0 0;
}
.btn_close_2{
	width:33px;
	background-image:url(../images/icon-SearchClose.png);
	background-size:30px;
	background-repeat: no-repeat; 
	float:left;
	height:30px;
}
#btn_open{
	font-size:15px;
}
.btn_open_1{
	font-size:15px;
	float:left;
	margin:6px 0 0 0;
}
.btn_open_2{
	width:33px;
	background-image:url(../images/icon-SearchOpen.png);
	background-size:30px;
	background-repeat: no-repeat; 
	float:left;
	height:30px;
}
#search_li_div_btn1{
	width:450px;
	float:left;
	height:20px;
}
#search_li_div_btn2{
	float:left;
}
.new_search_btn_container {
	/* display: flex;
    justify-content: center; 
    align-items: center;  */
	width: 100%;
}
.tr_9_18_00_B {
	font-family: '微軟正黑體';
	background-color: #aaa;
    text-align: center;
	height:35px;
	font-size:14px;
}
.tr_9_18_00_F {
	background: #fff;
    text-align: center;
	height:35px;
}
.tr_td1_col2{
	width: 10%;
    color: #fff;
    font-weight: bold;
}
.tr_td2_col2{
	padding: 7px;
    border: 1px solid #999;
	background-color: #fff;
	text-align:left;
	width: 90%;
}
.tr_td1_col4{
	width: 10%;
    color: #fff;
    font-weight: bold;
}
.tr_td2_col4{
	padding: 7px;
    border: 1px solid #999;
	background-color: #fff;
	text-align:left;
	width: 40%;
}
.tr_td1{
	width: 150px;
    color: #fff;
    font-weight: bold;
}
.tr_td2{
	padding: 7px;
    border: 1px solid #999;
	background-color: #fff;
	text-align:left;
	/* width: 35%; */
}
.text_red{
	color:red;
}
.btn_88{
	width:95px;
	height:25px;
	color:#fff;
	/* background-color:#C29C72; */
	background-color:#0082DA;
	font-size:16px;
	text-align:center;
	margin:10px 10px;
	border-radius: 15px;
	font-weight: bold;
}
.btn_88:hover{
	background-color: #000;
}

.btn_99{
	width:105px;
	height:25px;
	color:#fff;
	/* background-color:#C29C72; */
	background-color:#0082DA;
	font-size:14px;
	text-align:center;
	margin:10px 3%;
	border-radius: 15px;
	font-weight: bold;
}
.btn_99:hover{
	background-color: #66DD00;
}

/******************top*************************/
.top_left{
	text-align: center;
	height: 50px;
	float: left;
}
.top_center {
	position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
	/* height: 50px; */
}
.top_center > span{
	font-size: 24px;
    color: #000;
	font-weight: bold;
	/* top: 0px; */
    position: relative;
}
.top_right{
	font-family: '微軟正黑體' ,Arial, Helvetica, sans-serif;
	font-size: 18px; 
	font-weight: bold;
	color: #888888;
	line-height: 2em;
    /* height: 4em; */
	float: right;
	position: absolute;
    top: 50%;
    right: 0;
    transform: translate(0%, -50%);
	margin-right:10px;
}
.top_logo{
	height: 50px;
	float: left;
}
.top_btn_div{
	width: 100%;

}
.top_btn_clr_b{
	margin: 5px 0 5px 3%;
	/* background-color:#C29C72; */
	background-color:#0082DA;
    width: 100px;
	float: right;
	line-height: 2em;
    /* height: 2em; */
	color: #fff;
	border-radius: 15px;
	text-decoration: none;
    font-family: '微軟正黑體' ,Arial, Helvetica, sans-serif;
    font-size: 16px;
    text-align: center;
}
.top_btn_clr_b:hover {
	background-color: #000;
}
.download_btn_clr_b2{
	margin: 5px 0 5px 0;
	/* background-color:#C29C72; */
	background-color:#0082DA;
    /* width: 100px; */
	line-height: 2em;
    /* height: 2em; */
	color: #fff;
	border-radius: 15px;
	text-decoration: none;
    font-family: '微軟正黑體' ,Arial, Helvetica, sans-serif;
    font-size: 16px;
    text-align: center;
}
.download_btn_clr_b2:hover {
	background-color: #000;
}
.download_btn_flowchar {
	margin: 5px;
}
.download_center {
	text-align: center;
}

/**************************login.php*************************/
.loginBtn{
	display: inline-block;
	padding: 0 5%;
	border-radius: 15px;
	border: 0;
	/* background-color:#C29C72; */
	background-color:#0082DA;
	margin: 0 10px;
	height:30px;
	width:130px !important;
	color:#fff;
	font-size: 20px;
}
.loginText{
	margin: 10px !important;
	border-radius: 7px;
	height:30px !important;
	border: 1px #fff;
	width: 50%;
	font-size: 20px;
	
}
.loginFont{
	color:#000;
	font-size:25px;
	border-radius: 15px;
}
.code{
	margin: 0 0 20px 50%;
	border-radius: 7px;
}
.login_div{
	background-color: #fff;
	width: 305px;
	margin: 5% 35%;
	border-radius: 65px;
	padding: 3% 5%;
}
.login_logo{
	padding: 5% 0;
	height: auto;
	text-align: center;
}
.login_logo > img{
	width: 100%;
}
.login_form{
	padding: 5% 0;
	height: auto;
	text-align: center;
}
.login_acc{
	border:2px solid #aaa;
	border-radius: 25px;
	margin-bottom: 20px;
	font-size: 20px;
	color: #aaa;
}
.login_acc > span{
	padding-right: 20px;
	border-right: 2px solid;
}
.login_msg{
	margin-top: 10%;
	font-size: 25px;
	   color: red;
}
.login_cnt{
	background-color:#f4f4f6;
}

.ck-editor__editable{
	min-height: 300px;
}

.float-lf{
	float:left;
}

.zipcode{
	display: none;
}

.wd90{
	width:90%;
}

.wd100{
	width:100%;
}

.or_table{
	text-align: center;
    font-size: 16px;
}

.or_table td{
    padding: 10px;
    border: #CCC solid 1px;
}

.bg_FFEE99{
	background-color:#FFEE99;
}

.winDiv1{
	background-color: #CB394A; 
	color:#fff; 
	font-weight: 600;
	font-size: 25px;
    text-align: center;
}

.winDiv2{
	width: 20%;
    float: left;
    background-color: #092F52;
    padding: 20px 0;
    border-bottom: 1px solid #B59874;
    color: #B59874;
    text-align: center;
    font-size: 16px;
}

.LanguageDv{
	height: 30px;
	width: 100%;
	min-width: 220px;
	margin-bottom: 10px;
}
	
.LanguageDv li {
	float: left;
	padding: 0 0 0 10px;
	height: 20px;
	line-height: 30px;
}
	
.LanguageBtnList li a{
	width: 100%;
	color: #fff !important;
	text-decoration: none !important;
	font-family: '微軟正黑體' ,Arial, Helvetica, sans-serif;
	font-size: 14px;
	text-align: center;
}
	
.LanguageBtnList li a .active{
	margin: 5px 0 5px 3%;
	background-color: #000;
	width: 100px;
	float: right;
	line-height: 2em;
	height: 2em;
	color: #fff;
	border-radius: 15px;
}

.btn_Language {
	margin: 5px 0 5px 3%;
	/* background-color:#C29C72; */
	background-color:#0082DA;
	width: 100px;
	float: right;
	line-height: 2em;
	height: 2em;
	color: #fff;
	border-radius: 15px;
}
	
.btn_Language:hove {
	background-color: #000;
}

.ExecErrorTD{
	background-color: #CB394A;
	color: #fff;
	font-weight: 600;
	font-size: 25px;
	text-align: center;
	word-break: break-all;
}

.remove_bg_image {
	background: unset !important;
	background-color: #ffffff;
}