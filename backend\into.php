<?php
// ini_set('display_errors','on') ;
ini_set('display_errors','off') ;
require_once($_SERVER["DOCUMENT_ROOT"]."/resonac-all/sysconfig/config.php");
require_once(LIBRARY_DIR."library/classes/admin/Account.php");
require_once(LIBRARY_DIR."library/classes/utils/Tools.php");

$account_id = substr(trim($_REQUEST["id"]), 0, 10);
$passwd = substr(trim($_REQUEST["passwd"]), 0, 10);
$action = trim($_POST["action"]);
$show_confirm = false;
$masked_email = "";

// 處理忘記密碼
if ($action == 'forgot_password') {
    $oDB = new DB(DB_HOST, DB_NAME, DB_USER, DB_PASSWORD, true);
    if (!$oDB->open()) {
        die(CONNECTION_DB_FAILED.$oDB->error());
    }

    $oAccount = new Account($oDB);
    if ($oAccount->getFromDb($account_id) && $oAccount->status == '1') {
        $masked_email = UtilCtrl::maskEmail($oAccount->email);
        $show_confirm = true;
    } else {
        echo "<script>alert('查無此帳號或帳號已停用');</script>";
    }
}

// 處理寄送重設連結
if ($action == 'send_reset') {
    $account_id = trim($_POST['account_id']);

    $oDB = new DB(DB_HOST, DB_NAME, DB_USER, DB_PASSWORD, true);
    if (!$oDB->open()) {
        die(CONNECTION_DB_FAILED.$oDB->error());
    }

    $oAccount = new Account($oDB);
    if ($oAccount->getFromDb($account_id)) {
        // 產生重設 token
        $token = md5($account_id . time() . rand());

        // 儲存 token 到 session
        session_start();
        $_SESSION['reset_token_' . $token] = [
            'account_id' => $account_id,
            'expire_time' => time() + 1800 // 30分鐘後過期
        ];

        // 寄送郵件
        $result = UtilCtrl::phpmailerForgotPassword($oAccount->email, $token, $account_id);

        if ($result == "") {
            echo "<script>alert('重設密碼連結已寄送至您的信箱，請於30分鐘內完成重設');location.href='into.php';</script>";
        } else {
            echo "<script>alert('郵件發送失敗：" . addslashes($result) . "');history.back();</script>";
        }
    }
}

// 處理登入
if (substr(trim($_POST["login"]), 0, 1)=="1") {
  $oDB = new DB(DB_HOST, DB_NAME, DB_USER, DB_PASSWORD, true);
  if (!$oDB->open()) {
    die(CONNECTION_DB_FAILED.$oDB->error());
  }
  $oAccount = new Account($oDB);
  $login = $oAccount->isAuthorized($account_id, $passwd);
  if ($login == "S") {
    session_start();
    // session_register("ss_account");
    $_SESSION['ss_account'] = serialize($oAccount);
    // 產品沒有要開放後台
    // $url = DOMAIN_BACKEND_URL."PD/PRodList.php";
    $url = DOMAIN_BACKEND_URL."NEWS/NewsList.php";
    header("Location:".$url);
    exit(0);
  } else {
    if ($login == "F") {	//登入帳號或密碼錯誤
      $logon_count = $oAccount->getLogonCount();
      if ($logon_count == "" || $logon_count == null) {
        $logon_count = "999";
      } else {
        $logon_count = "000";
      }
    } elseif ($login == "T") {  //登入時間非在指定有效時間內
      $logon_count = "OUT OF TIME";
    } elseif ($login == "I") {	//登入IP不是在限定IP內
      $logon_count = "ILLEGAL IP";
    }
    header("Location:".NO_PRIV_PAGE."?action=$logon_count");
  }
}
?>
<!DOCTYPE html>
<html lang="zh-TW">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>RESONAC - 後台管理系統</title>
<style type="text/css">
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', 'Microsoft JhengHei', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.login-container {
    background: white;
    border-radius: 15px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    padding: 40px;
    width: 100%;
    max-width: 400px;
    text-align: center;
}

.logo-section {
    margin-bottom: 30px;
}

.logo-section img {
    max-width: 180px;
    height: auto;
    margin-bottom: 10px;
}

.logo-subtitle {
    color: #666;
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 20px;
    text-align: left;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #333;
    font-weight: 500;
    font-size: 14px;
}

.form-group input {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s ease;
    background-color: #f8f9fa;
}

.form-group input:focus {
    outline: none;
    border-color: #00B8D4;
    background-color: white;
}

.button-group {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

.btn {
    flex: 1;
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-login {
    background-color: #00B8D4;
    color: white;
}

.btn-login:hover {
    background-color: #0097A7;
    transform: translateY(-2px);
}

.btn-clear {
    background-color: #4CAF50;
    color: white;
}

.btn-clear:hover {
    background-color: #45a049;
    transform: translateY(-2px);
}

.forgot-password {
    margin-top: 15px;
}

.forgot-password a {
    color: #00B8D4;
    text-decoration: none;
    font-size: 14px;
    transition: color 0.3s ease;
}

.forgot-password a:hover {
    color: #0097A7;
    text-decoration: underline;
}

.confirm-section {
    background: white;
    border-radius: 15px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    padding: 30px;
    width: 100%;
    max-width: 450px;
    text-align: center;
    margin-top: 20px;
}

.confirm-section h3 {
    color: #00B8D4;
    margin-bottom: 20px;
    font-size: 20px;
}

.confirm-section p {
    margin: 10px 0;
    color: #333;
    font-size: 14px;
}

.confirm-section .btn {
    margin: 5px;
    min-width: 100px;
}

.btn-cancel {
    background-color: #6c757d;
    color: white;
}

.btn-cancel:hover {
    background-color: #5a6268;
}

@media (max-width: 480px) {
    .login-container, .confirm-section {
        padding: 30px 20px;
    }

    .button-group {
        flex-direction: column;
    }

    .btn {
        margin-bottom: 10px;
    }
}
</style>
<script>
function chkValue() {
    var f = document.getElementById('loginForm');
    var id = document.getElementById('id').value;
    var passwd = document.getElementById('passwd').value;

    if (id == "") {
        document.getElementById('id').focus();
        alert("請輸入帳號");
        return false;
    }
    if (passwd == "") {
        document.getElementById('passwd').focus();
        alert("請輸入密碼");
        return false;
    }

    document.getElementById('login').value = "1";
    f.submit();
}

function resetValue() {
    document.getElementById('id').value = "";
    document.getElementById('passwd').value = "";
    document.getElementById('id').focus();
}

function forgotPassword() {
    var id = document.getElementById('id').value;
    if (id == "") {
        document.getElementById('id').focus();
        alert("請先輸入帳號");
        return false;
    }

    document.getElementById('action').value = 'forgot_password';
    document.getElementById('loginForm').submit();
}
</script>
</head>

<body>
<div class="login-container">
    <div class="logo-section">
        <img src="../images/logo.svg" alt="RESONAC" style="max-width: 200px; height: auto;">
        <div class="logo-subtitle">後台管理系統</div>
    </div>

    <form method="post" id="loginForm" action="into.php">
        <input type="hidden" name="login" id="login" value="">
        <input type="hidden" name="action" id="action" value="">

        <div class="form-group">
            <label for="id">ID:</label>
            <input type="text" name="id" id="id" maxlength="10" autocomplete="username">
        </div>

        <div class="form-group">
            <label for="passwd">Password:</label>
            <input type="password" name="passwd" id="passwd" maxlength="10" autocomplete="current-password">
        </div>

        <div class="button-group">
            <button type="button" class="btn btn-login" onclick="chkValue()">Login</button>
            <button type="button" class="btn btn-clear" onclick="resetValue()">Clear</button>
        </div>

        <div class="forgot-password">
            <a href="javascript:forgotPassword()">Forgot Password?</a>
        </div>
    </form>
</div>


<?php if ($show_confirm): ?>
<div class="confirm-section">
    <h3>密碼重設確認</h3>
    <p><strong>帳號：</strong><?php echo htmlspecialchars($account_id); ?></p>
    <p><strong>將寄送重設連結至：</strong><?php echo $masked_email; ?></p>

    <form method="post" action="into.php">
        <input type="hidden" name="action" value="send_reset">
        <input type="hidden" name="account_id" value="<?php echo htmlspecialchars($account_id); ?>">
        <button type="submit" class="btn btn-login">確認寄送</button>
        <button type="button" class="btn btn-cancel" onclick="location.href='into.php'">取消</button>
    </form>
</div>
<?php endif; ?>

</body>
</html>