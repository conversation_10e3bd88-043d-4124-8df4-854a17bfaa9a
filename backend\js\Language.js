function switchLanguage(selector, lang_id){
	$(".LanguageBtnList li a div").removeClass( "active");
	$(selector).children( "div" ).addClass("active");
	$(".LanguageInputArea").hide();
	$("#LanguageInputArea"+lang_id).show();
    // 中文
	if(lang_id == "1"){
		$(".LanguageInputArea table.Language_tab tr td").attr('style',  'background-color:#fff');
    // English
	}else if(lang_id == "2"){
		$(".LanguageInputArea table.Language_tab tr td").attr('style',  'background-color:#fff9e4');
	}
}
