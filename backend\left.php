<script>
function product_panel(id_name) {
	if (document.getElementById(id_name).style.display=="none") {
		document.getElementById(id_name).style.display="block";
	} else {
		document.getElementById(id_name).style.display="none";
	}
}
</script>

<style>
/* 導航按鈕樣式 */
.nav-button {
  display: block;
  width: 125px;
  height: 29px;
  line-height: 29px;
  text-align: center;
  text-decoration: none;
  font-size: 12px;
  font-family: Verdana, Arial, Helvetica, sans-serif;
  font-weight: normal;
  color: white;
  background: linear-gradient(to bottom, #4A9EFF 0%, #007AD7 50%, #005BA3 100%);
  border: 1px solid #005BA3;
  border-radius: 3px;
  box-shadow:
    inset 0 1px 0 rgba(255,255,255,0.3),
    0 1px 2px rgba(0,0,0,0.2);
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.nav-button:before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.nav-button:hover {
  background: linear-gradient(to bottom, #7BC142 0%, #5BA82C 50%, #4A8A23 100%);
  border-color: #4A8A23;
  transform: translateY(-1px);
  box-shadow:
    inset 0 1px 0 rgba(255,255,255,0.4),
    0 2px 4px rgba(0,0,0,0.3);
}

.nav-button:hover:before {
  left: 100%;
}

.nav-button:active {
  transform: translateY(0);
  box-shadow:
    inset 0 1px 0 rgba(255,255,255,0.2),
    0 1px 1px rgba(0,0,0,0.2);
}

/* 確保文字不被選取 */
.nav-button {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
</style>
<tr>
        <th scope="row"><table width="130" border="1" align="center" cellpadding="0" cellspacing="1" bordercolor="#FFFFFF">
          <tr>
            <th colspan="4" bordercolor="#007AD7" class="style1" scope="row"><table width="125" border="0" cellspacing="0" cellpadding="0">
              <tr>
                <th height="20" class="style1" scope="row">HI,管理者 您好</th>
              </tr>
              <tr>
                <th height="20" class="style1" scope="row"><a href="../logout.php" class="style1">登出</a></th>
              </tr>
            </table></th>
          </tr>
          
        </table></th>
      </tr>
      <tr>
        <th scope="row">&nbsp;</th>
      </tr>
      <tr>
        <th scope="row"><table width="125" height="87" border="0" align="center" cellpadding="0" cellspacing="0" id="___01">
          <tr>
            <td height="29">
              <a href="../NEWS/NewsList.php" class="nav-button">
                最新消息
              </a>
            </td>
          </tr>
          <tr>
            <td height="5"></td>
          </tr>
          <tr>
            <td height="29">
              <a href="../CONVERT/ConvertList.php" class="nav-button">
                轉檔工具
              </a>
            </td>
          </tr>
		  <tr>
			<!-- <td id="product_id" style="display:none" align="left" height="29">
				<p>‧<a href="../PM/PMainList.php">產品類別</a></p>
				‧<a href="../PD/PRodList.php">產品資料</a>
			</td> -->
		  </tr>
        </table></th>
      </tr>