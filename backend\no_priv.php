<?php
require_once($_SERVER["DOCUMENT_ROOT"]."/resonac-all/sysconfig/config.php");
$action = $_REQUEST["action"];
session_start();
session_unset();
session_destroy();
?>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>登入失敗</title>
</head>
<body bgcolor="#FFFFFF" text="#000000">
<br>
<br>
<table width=760 height=308 border=0 align="center" cellpadding=0 cellspacing=0>
  <tr>
    <td width="9" height="418"></td>
    <td align="center" valign="middle"><table width="387" height="151" border="0" cellpadding="0" cellspacing="0">
        <tr>
          <td width="215" rowspan="2">&nbsp;</td>
          <td width="172" valign="top"></td>
        </tr>
        <tr>
          <td align="center">
            <table width="68%" border="0" align="center" cellpadding="3" cellspacing="1"  bordercolor="#4B87C2">
              <tr>
                <td width="100%" height="40" align="center"><font size="4" color="#FF0000"><b>登入失敗 (<?php echo $action?>)</b></font></td>
              </tr>
              <tr height="50" >
                <td colspan="2" align="center" ><a href="<?=LOGIN_PAGE?>" target="_top">重新登入系統</a></td>
              </tr>
            </table></td>
        </tr>
      </table>
        <table width="100%" border="0" cellspacing="0" cellpadding="0">
          <tr>
            <td align="center">&nbsp;
            </td>
          </tr>
      </table></td>
    <td width="9" height="418">&nbsp;</td>
  </tr>
</table>
</body>
</html>
