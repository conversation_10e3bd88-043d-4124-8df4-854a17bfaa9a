<?php
include("top.php") ?>
<!DOCTYPE html>
<html lang="<?php echo (__LANGUAGE != '2')?'zh-Hant-TW':'en';?>">
  <head>
    <?php include("head.php");?>
    <title><?php echo $__dataAry['contact_page']['title'];?> | <?php echo $__dataAry['company_name'];?></title>
    <meta name="description" content="<?php echo $__dataAry['contact_page']['meta_description'];?>">
    <meta name="copyright" content="<?php echo $__dataAry['contact_page']['meta_copyright'];?>">
    <meta property="og:type" content="<?php echo $__dataAry['contact_page']['meta_og_type'];?>">
    <meta property="og:site_name" content="<?php echo $__dataAry['contact_page']['meta_og_site_name'];?>">
    <meta property="og:title" content="<?php echo $__dataAry['contact_page']['meta_og_title'];?>">
    <meta property="og:description" content="<?php echo $__dataAry['contact_page']['meta_og_description'];?>">
    <meta property="og:url" content="<?php echo $__dataAry['contact_page']['meta_og_url'];?>">
    <meta property="og:image" content="<?php echo $__dataAry['contact_page']['meta_og_image'];?>">
    <script type="text/javascript">
      function MM_preloadImages() { //v3.0
        var d=document; if(d.images){ if(!d.MM_p) d.MM_p=new Array();
          var i,j=d.MM_p.length,a=MM_preloadImages.arguments; for(i=0; i<a.length; i++)
          if (a[i].indexOf("#")!=0){ d.MM_p[j]=new Image; d.MM_p[j++].src=a[i];}}
      }
      function MM_swapImgRestore() { //v3.0
        var i,x,a=document.MM_sr; for(i=0;a&&i<a.length&&(x=a[i])&&x.oSrc;i++) x.src=x.oSrc;
      }
      function MM_findObj(n, d) { //v4.01
        var p,i,x;  if(!d) d=document; if((p=n.indexOf("?"))>0&&parent.frames.length) {
          d=parent.frames[n.substring(p+1)].document; n=n.substring(0,p);}
        if(!(x=d[n])&&d.all) x=d.all[n]; for (i=0;!x&&i<d.forms.length;i++) x=d.forms[i][n];
        for(i=0;!x&&d.layers&&i<d.layers.length;i++) x=MM_findObj(n,d.layers[i].document);
        if(!x && d.getElementById) x=d.getElementById(n); return x;
      }
      function MM_swapImage() { //v3.0
        var i,j=0,x,a=MM_swapImage.arguments; document.MM_sr=new Array; for(i=0;i<(a.length-2);i+=3)
        if ((x=MM_findObj(a[i]))!=null){document.MM_sr[j++]=x; if(!x.oSrc) x.oSrc=x.src; x.src=a[i+2];}
      }
      function MM_openBrWindow(theURL,winName,features) { //v2.0
        window.open(theURL,winName,features);
      }
      function MM_validateForm() { //v4.0
        //1 = 中文 2 = 英文
        var lang = '<?php echo __LANGUAGE?>';
        if (document.getElementById){
          var i,p,q,nm,test,num,min,max,errors='',args=MM_validateForm.arguments;
          var tel_f = /^\(([0-9]{2,4})\)([0-9]{6,8})/;
          for (i=0; i<(args.length-2); i+=3) {
            test=args[i+2]; val=document.getElementById(args[i]);
            if (val) {
              nm=val.id;
              if ((val=val.value)!="") {
                if (nm == "連絡電話" || nm == "Telephone") {
                  if (tel_f.test(val)==false){
                    if(lang == "1"){
                      errors+='- '+nm+' 請依照(02)28989889的格式.\n';
                    }else{
                      errors+='- '+nm+' Please follow the format of (02)28989889.\n';
                    }
                  }
                }
                if (test.indexOf('isEmail')!=-1) {
                  p=val.indexOf('@');
                  if (p<1 || p==(val.length-1)){
                   if(lang == "1"){
                    errors+='- '+nm+' 不是正確的EMAIL格式.\n';
                   }else{
                    errors+='- '+nm+' Not in the correct EMAIL format.\n';
                   } 
                  }
                } else if (test!='R') {
                  num = parseFloat(val);
                  if (isNaN(val)){
                    if(lang == "1"){
                      errors+='- '+nm+' 只能填入數字.\n';
                    }else{
                      errors+='- '+nm+' Only numbers can be entered.\n';
                    }
                  }
                  if (test.indexOf('inRange') != -1) {
                    p=test.indexOf(':');
                    min=test.substring(8,p); max=test.substring(p+1);
                    if (num<min || max<num) errors+='- '+nm+' Must Contain a Number Between '+min+' and '+max+'.\n';
                  }
                }
              } else if (test.charAt(0) == 'R'){
                if(lang == "1"){
                  errors += '- '+nm+' 是必填欄位.\n';
                }else{
                  errors += '- '+nm+' is a required field.\n';
                }
              }
            }
          }
          if (errors){
            if(lang == "1"){
              alert('發生以下錯誤:\n'+errors);
            }else{
              alert('The following error occurred:\n'+errors);
            }
          }
          document.MM_returnValue = (errors =='');
        }
      }
      function chgAuth() {
        $("#code").attr("src", "GetCode.php?AuthNum=" + Math.random()) ;
      }
    </script>
    <script src="Scripts/AC_RunActiveContent.js" type="text/javascript"></script>
  </head>
  <body class="<?php echo (__LANGUAGE != '2')?'':'en';?>">
    <?php include("navbar.php");?>
    <nav class="breadcrumb">
      <div class="breadcrumb__block">
        <div class="breadcrumb__item">
          <a class="breadcrumb__link" href="index.php"><?php echo $__dataAry['home'];?></a>
        </div>
        <div class="breadcrumb__item"><?php echo $__dataAry['contact_page']['title'];?></div>
      </div>
    </nav>
    <section class="banner">
      <div class="container">
        <h2 class="banner__title wow fadeIn"><?php echo $__dataAry['contact_page']['title'];?></h2>
      </div>
    </section>
    <section class="contact">
      <div class="container">
        <div class="contact__wrap">
          <div class="contact__block">
      <?php foreach($__dataAry['contact_page']['item_data'] as $item){ ?>
            <div class="contact__item wow fadeIn">
              <div class="contact__item-head" style="background-image: url('<?php echo $item['div_img'];?>')"></div>
              <div class="contact__item-body"> 
                <h3 class="contact__item-title"><?php echo $item['title'];?></h3>
                <table class="contact__table">
                  <tr class="contact__table-tr">
                    <td class="contact__table-td"><?php echo $__dataAry['contact_page']['item_addr'];?></td>
                    <td class="contact__table-td"><?php echo $item['addr'];?></td>
                  </tr>
                  <tr class="contact__table-tr">
                    <td class="contact__table-td"><?php echo $__dataAry['contact_page']['item_phone'];?></td>
                    <td class="contact__table-td"><?php echo $item['phone'];?></td>
                  </tr>
                  <tr class="contact__table-tr">
                    <td class="contact__table-td"><?php echo $__dataAry['contact_page']['item_fax'];?></td>
                    <td class="contact__table-td"><?php echo $item['fax'];?></td>
                  </tr>
                </table>
                <a class="btn btn--link" href="<?php echo $item['url_link'];?>" target="_blank" rel="<?php echo $item['url_rel'];?>">
                  <?php echo $__dataAry['contact_page']['btn_map'];?>
                </a>
              </div>
            </div>
      <?php } ?>
          </div>
        </div>
        <div class="contact__form">
          <div class="contact__form-wrap wow fadeIn">
            <div class="contact__form-text">
              <?php echo $__dataAry['contact_page']['show_text'];?>
            </div>
            <div class="contact__form-block">
              <form class="form" name="form1" method="post" action="contact.php">
                <div class="form__block">
                  <div class="form__row">
                    <div class="form__col">
                      <div class="form__item">
                        <label class="form__label"><?php echo $__dataAry['contact_page']['show_name'];?></label>
                        <input class="form__input" name="name" type="text" id="<?php echo $__dataAry['contact_page']['show_name'];?>">
                      </div>
                    </div>
                    <div class="form__col"> 
                      <div class="form__item"> 
                        <label class="form__label"><?php echo $__dataAry['contact_page']['show_company_name'];?></label>
                        <input class="form__input" name="company" type="text" id="<?php echo $__dataAry['contact_page']['show_company_name'];?>">
                      </div>
                    </div>
                  </div>
                  <div class="form__row">
                    <div class="form__col">
                      <div class="form__item">
                        <label class="form__label"><?php echo $__dataAry['contact_page']['show_phone'];?></label>
                        <input class="form__input" name="tel" type="text" id="<?php echo $__dataAry['contact_page']['show_phone'];?>">
                      </div>
                    </div>
                    <div class="form__col"> 
                      <div class="form__item"> 
                        <label class="form__label"><?php echo $__dataAry['contact_page']['show_email'];?></label>
                        <input class="form__input" name="email" type="text" id="<?php echo $__dataAry['contact_page']['show_email'];?>">
                      </div>
                    </div>
                  </div>
                  <div class="form__row">
                    <div class="form__col">
                      <div class="form__item">
                        <label class="form__label"><?php echo $__dataAry['contact_page']['show_p_class'];?></label>
                        <input class="form__input" name="pmain" type="text" id="<?php echo $__dataAry['contact_page']['show_p_class'];?>">
                      </div>
                    </div>
                    <div class="form__col"> 
                      <div class="form__item"> 
                        <label class="form__label"><?php echo $__dataAry['contact_page']['show_sub'];?></label>
                        <input class="form__input" name="subj" type="text" id="<?php echo $__dataAry['contact_page']['show_sub'];?>">
                      </div>
                    </div>
                  </div>
                  <div class="form__row">
                    <div class="form__col form__col--colspan">
                      <div class="form__item">
                        <label class="form__label"><?php echo $__dataAry['contact_page']['show_content'];?></label>
                        <textarea class="form__textarea" name="content" id="<?php echo $__dataAry['contact_page']['show_content'];?>" cols="45" rows="5"></textarea>
                      </div>
                    </div>
                  </div>
                  <div class="form__row">
                    <div class="form__col">
                      <div class="form__item">
                        <label class="form__label"><?php echo $__dataAry['contact_page']['show_code'];?></label>
                        <input class="form__input" name="AuthNum" type="text" id="<?php echo $__dataAry['contact_page']['show_code'];?>">
                      </div>
                    </div>
                    <div class="form__col">
                      <div class="form__item">
                        <label class="form__label"></label>
                        <a href="#" onclick='javascript:chgAuth();return false;'>
                          <img src="GetCode.php" alt="<?php echo $__dataAry['contact_page']['show_code'];?>" width="100" height="40" class="code" id="code">
                        </a>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="form__btn">
                  <button class="btn" type="reset"><?php echo $__dataAry['contact_page']['btn_reset'];?></button>
                  <button class="btn btn--dark" type="submit" onclick="MM_validateForm('<?php echo $__dataAry['contact_page']['show_name'];?>','','R',
                                                                                      '<?php echo $__dataAry['contact_page']['show_company_name'];?>','','R',
                                                                                      '<?php echo $__dataAry['contact_page']['show_phone'];?>','','R',
                                                                                      '<?php echo $__dataAry['contact_page']['show_email'];?>','','RisEmail',
                                                                                      '<?php echo $__dataAry['contact_page']['show_p_class'];?>','','R',
                                                                                      '<?php echo $__dataAry['contact_page']['show_sub'];?>','','R',
                                                                                      '<?php echo $__dataAry['contact_page']['show_content'];?>','','R',
                                                                                      '<?php echo $__dataAry['contact_page']['show_code'];?>','','R');return document.MM_returnValue;"><?php echo $__dataAry['contact_page']['btn_submit'];?></button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </section>
    <?php include("footer.php");?>
  </body>
</html>