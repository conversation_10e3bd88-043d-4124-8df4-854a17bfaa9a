<?php
// ini_set('display_errors','on') ;
require_once($_SERVER["DOCUMENT_ROOT"]."/resonac-all/sysconfig/config.php");
require_once(LIBRARY_DIR."library/classes/utils/Tools.php");

$name = trim($_POST["name"]);
$company = trim($_POST["company"]);
$tel = trim($_POST["tel"]);
$email = trim($_POST["email"]);
$pmain = trim($_POST["pmain"]);
$subj = trim($_POST["subj"]);
$content = trim($_POST["content"]);

session_start() ;
$AuthNum = substr(trim($_POST["AuthNum"]), 0, 4);
$AuthResult = $_SESSION["AuthResult"];

if ($AuthNum == $AuthResult) {
    if($name == '' || $company == '' || $tel == '' || $email == '' || $pmain == '' || $subj == '' || $content == ''){
		echo "<script>alert('必填欄位不可為空!');location.href='index.php';</script>";
	}else{
		$msg = UtilCtrl::phpmailerContact($name,$company,$tel,$email,$pmain,$subj,$content) ;
		if ($msg == "") {
			echo "<script>alert('送出成功');location.href='index.php';</script>";
		}
	}
} else {
	echo "<script>alert('驗證碼錯誤!');history.go(-1);</script>";
}

?>