<?php
include("top.php") ?>
<!DOCTYPE html>
<html lang="<?php echo (__LANGUAGE != '2')?'zh-Hant-TW':'en';?>">
  <head>
<?php
    include("head.php");
    require_once(LIBRARY_DIR."library/classes/admin/Control.php");
    require_once(LIBRARY_DIR."library/classes/admin/Account.php");
    require_once(LIBRARY_DIR."library/classes/admin/News.php");
    $oDB = new DB(DB_HOST, DB_NAME, DB_USER, DB_PASSWORD, true);
    if (!$oDB->open()) {
      die(CONNECTION_DB_FAILED.$oDB->error());
    }
    $page = trim($_GET["page"]);
    $oNewsList = new NewsList($oDB);
    $oNewsList->getNewsList("1",__LANGUAGE);
    $setpage = 7;
    $oNewsList->setPageSize($setpage);
    $page = intval($page);
    $page = ($page < 1) ? 1 : $page;
    $page = ($page == ENDPAGE || $page > $oNewsList->totalPage)?$oNewsList->totalPage:$page;
    $oNewsList->getPage($page);
    $total = $oNewsList->size(); ?>
    <title><?php echo $__dataAry['company_name'];?></title>
    <meta name="description" content="<?php echo $__dataAry['index_page']['meta_description'];?>">
    <meta name="copyright" content="<?php echo $__dataAry['index_page']['meta_copyright'];?>">
    <meta property="og:type" content="<?php echo $__dataAry['index_page']['meta_og_type'];?>">
    <meta property="og:site_name" content="<?php echo $__dataAry['index_page']['meta_og_site_name'];?>">
    <meta property="og:title" content="<?php echo $__dataAry['index_page']['meta_og_title'];?>">
    <meta property="og:description" content="<?php echo $__dataAry['index_page']['meta_og_description'];?>">
    <meta property="og:url" content="<?php echo $__dataAry['index_page']['meta_og_url'];?>">
    <meta property="og:image" content="<?php echo $__dataAry['index_page']['meta_og_image'];?>">
    <script type="application/ld+json"><?php echo $__dataAry['index_page']['id_json'];?></script>
  </head>
  <body class="<?php echo (__LANGUAGE != '2')?'':'en';?>">
<?php include("navbar.php");
      switch(__LANGUAGE){
        case "2":
          include("language/en/index.php");
        break;
        default:
          include("language/zh-TW/index.php");
        break;
      }
      include("footer.php");?>
  </body>
</html>