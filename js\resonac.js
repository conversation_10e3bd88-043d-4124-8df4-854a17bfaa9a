$(document).ready(function () {
  'use strict';
  new WOW().init();

  //選單
  $('.menu__item-link').on('click', function (e) {
    if (window.matchMedia('(max-width: 1100px)').matches) {
      return;
    }
    if ($(this).parents('.dropdown').length > 0) {
      e.preventDefault();
      var clicked = $(this);
      $('.navbar__languages').removeClass('opened');
      clicked.toggleClass('opened');
      if (clicked.hasClass('opened')) {
        clicked.siblings('.dropdown__wrap').slideDown(350).css('display', 'flex');
      }else{
        clicked.siblings('.dropdown__wrap').slideUp(350);
      }
    }
  });

  $('.navbar__languages-current').on('click', function (e) {
    e.preventDefault();
    $(this).parents('.navbar__languages').toggleClass('opened');
    $('.dropdown.opened').removeClass('opened');
    $('.dropdown__wrap').slideUp(350);
  });

  $('.navbar__languages-btn').on('click', function (e) {
    e.preventDefault();
    $('.navbar__languages').removeClass('opened');
  });

  $('.dropdown__btn').on('click', function (e) {
    e.preventDefault();
    $('.dropdown').removeClass('opened');
    $('.dropdown__wrap').slideUp(350);
  });

  $('.dropdown__body-link').on('click', function (e) {
    $('.dropdown').removeClass('opened');
    $('.dropdown__wrap').slideUp(350);
  });

  $('.navbar__toggle').on('click', function (e) {
    e.preventDefault();
    $(this).closest('.navbar').toggleClass('opened');
  });
  //選單 END

  //首頁輪播
  if($('.kv__block').length > 0){
    $('.kv__block').slick({
      slidesToShow: 1,
      slidesToScroll: 1,
      autoplay: true,
      autoplaySpeed: 5000,
      infinite: true,
      arrows: false,
      dots: false,
    });
  }
  //首頁輪播 END

  //麵包屑
  if ($('.breadcrumb').length > 0) {
    $('.breadcrumb').mCustomScrollbar({
        theme: "minimal-dark",
        axis: 'x',
        advanced: {
            autoExpandHorizontalScroll: true
        }
    });
  }
  //麵包屑 END

  //pop
  $(document).on('lity:open', function(event, instance) {
    if($('.certificates').length > 0){
      $('body').css('overflow', 'hidden');
    }
  });
  $(document).on('lity:remove', function(event, instance) {
    if($('.certificates').length > 0){
      $('body').removeAttr('style');
    }
  });
  //pop END

  $('.company__menu-item[href^="#"]').on('click', function (e) {
    e.preventDefault();

    var target = this.hash;
    var $target = $(target);
    $('html, body').stop().animate({
      'scrollTop': $target.offset().top
    }, 900, 'swing');
  });

});