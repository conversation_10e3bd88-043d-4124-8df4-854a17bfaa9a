<?php
require_once(LIBRARY_DIR."library/classes/admin/Language.php");
if(!isset($_SESSION)) {
	session_start();
}
$langCode = ( isset($_GET["lang"]) == true ) ? htmlspecialchars($_GET["lang"]) : "";

$oLanguage =  new Language();
if(isset($_SESSION["__lang"]) == false) { // 初次
	if($langCode != ""){
		$_SESSION["__lang"] = $oLanguage->getLangForCode($langCode);
	}else{
		$code = htmlspecialchars(substr($_SERVER['HTTP_ACCEPT_LANGUAGE'], 0, 2));
		$_SESSION["__lang"] = $oLanguage->getLangForCode($code);
	}
}else{
	if($langCode != ""){
		$_SESSION["__lang"] = $oLanguage->getLangForCode($langCode);
	}else{
		$_SESSION["__lang"] = $oLanguage->getLang($_SESSION["__lang"]);
	}
}

if($langCode != ""){
	$_SESSION["isSetLang"] = 1;
}

define("__LANGUAGE",$_SESSION["__lang"]); 

$__LanguageAry = $oLanguage->Load_Language(__LANGUAGE, "array");

switch(__LANGUAGE){
	case "2":
		$__dataAry = require_once("language/en.php");
	break;
	default:
		$__dataAry = require_once("language/zh-TW.php");
	break;
}
?>