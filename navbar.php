
<header class="navbar">
  <div class="navbar__block">
    <div class="navbar__head"><a class="navbar__logo" href="index.php"><img src="images/logo.svg" alt="<?php echo $__dataAry['navbar_img_alt'];?>"></a>
      <div class="navbar__toggle">
        <svg class="hb" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 10 10" stroke-width=".6" fill="rgba(0,0,0,0)" stroke-style="cursor: pointer">
          <path d="M2,3L5,3L8,3M2,5L8,5M2,7L5,7L8,7">
            <animate dur="0.2s" attributeName="d" values="M2,3L5,3L8,3M2,5L8,5M2,7L5,7L8,7;M3,3L5,5L7,3M5,5L5,5M3,7L5,5L7,7" fill="freeze" begin="jsMenuStart.begin" />
            <animate dur="0.2s" attributeName="d" values="M3,3L5,5L7,3M5,5L5,5M3,7L5,5L7,7;M2,3L5,3L8,3M2,5L8,5M2,7L5,7L8,7" fill="freeze" begin="jsMenuReverse.begin" />
          </path>
          <rect width="10" height="10" stroke="none">
            <animate dur="2s" id="jsMenuReverse" attributeName="width" begin="click" />
          </rect>
          <rect width="10" height="10" stroke="none">
            <animate dur="0.001s" id="jsMenuStart" attributeName="width" values="10;0" fill="freeze" begin="click" />
            <animate dur="0.001s" attributeName="width" values="0;10" fill="freeze" begin="jsMenuReverse.begin" />
          </rect>
        </svg>
      </div>
      <div class="navbar__company-name"><?php echo $__dataAry['company_name'];?></div>
    </div>
    <div class="navbar__body">
      <div class="navbar__menu">
        <ul class="menu">
    <?php foreach($__dataAry['menus'] as $item){
            echo "<li class='menu__item'>";
            if($item['is_next'] == '0'){
              echo "<a class='menu__item-link' href='".$item['url_link']."'>".$item['show_text']."</a>";
            }else{
              echo "<div class='dropdown'>
                      <div class='menu__item-link hide--mobile'>".$item['show_text']."</div>
                      <a class='menu__item-link show--mobile' href='".$item['url_link']."'>".$item['show_text']."</a>
                      <div class='dropdown__wrap'>
                        <div class='dropdown__head'><a class='dropdown__head-title' href='".$item['url_link']."'>".$item['show_text']."</a></div>
                        <div class='dropdown__body'>
                          <ul class='dropdown__body-lists'>";
                          foreach($item['next_data'] as $item2){
                      echo "<li class='dropdown__body-list'>
                              <a class='dropdown__body-link' href='".$item2['url_link']."'>".$item2['show_text']."</a>
                            </li>";
                          }
                      echo "</ul>
                          <div class='dropdown__btn'>".$__dataAry['btn_close']."</div>
                        </div>
                      </div>
                    </div>";
            }
            echo "</li>";
          } ?>
        </ul>
        <!-- <ul class="menu">
          <li class="menu__item">
            <a class="menu__item-link" href="index.html">首頁</a>
          </li>
          <li class="menu__item">
            <a class="menu__item-link" href="news-release.php">最新消息</a>
          </li>
          <li class="menu__item">
            <div class="dropdown">
              <div class="menu__item-link hide--mobile">公司簡介</div>
              <a class="menu__item-link show--mobile" href="company-profile.php">公司簡介</a>
              <div class="dropdown__wrap">
                <div class="dropdown__head"><a class="dropdown__head-title" href="company-profile.php">公司簡介</a></div>
                <div class="dropdown__body">
                  <ul class="dropdown__body-lists">
                    <li class="dropdown__body-list">
                      <a class="dropdown__body-link" href="company-profile.php">公司簡介</a>
                    </li>
                    <li class="dropdown__body-list">
                      <a class="dropdown__body-link" href="company-profile.php#our-code-of-conduct">企業行動規範</a>
                    </li>
                    <li class="dropdown__body-list">
                      <a class="dropdown__body-link" href="company-profile.php#corporate-philosophy">集團經營理念</a>
                    </li>
                  </ul>
                  <div class="dropdown__btn">關閉</div>
                </div>
              </div>
            </div>
          </li>
          <li class="menu__item">
            <a class="menu__item-link" href="products.php">產品介紹</a>
          </li>
          <li class="menu__item">
            <a class="menu__item-link" href="certificates.php">認證標章</a>
          </li>
          <li class="menu__item">
            <a class="menu__item-link" href="contact-us.php">聯絡我們</a>
          </li>
          <li class="menu__item">
            <a class="menu__item-link" href="affiliates.php">關係企業</a>
          </li>
        </ul> -->
      </div>
      <div class="navbar__actions">
        <div class="navbar__actions-languages">
          <div class="navbar__languages">
            <div class="navbar__languages-current">
              <div class="navbar__languages-icon">
                <svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 18 18" style="enable-background:new 0 0 18 18;" xml:space="preserve">
                  <path style="fill-rule:evenodd;clip-rule:evenodd;" d="M10.9,1.5C10.3,0.5,9.7,0,9,0C8.3,0,7.7,0.5,7.1,1.5
                    c-0.6,1-1,2.3-1.2,3.9h6.2C11.8,3.8,11.4,2.5,10.9,1.5z M5.7,11h6.5c0.1-0.8,0.1-1.5,0.1-2.2s0-1.5-0.1-2.2H5.7
                    C5.7,7.2,5.6,8,5.6,8.8S5.7,10.2,5.7,11L5.7,11z M14.8,2.3c-1-0.9-2.1-1.5-3.3-1.9c0.8,1.2,1.4,2.8,1.8,5H17
                    C16.5,4.2,15.8,3.1,14.8,2.3z M3.2,2.3c-1,0.9-1.7,1.9-2.2,3.1h3.8c0.3-2.2,0.9-3.8,1.8-5C5.3,0.8,4.1,1.4,3.2,2.3z M13.4,6.5
                    c0.1,0.8,0.1,1.5,0.1,2.2c0,0.7,0,1.5-0.1,2.2h4c0.2-0.8,0.3-1.5,0.3-2.2s-0.1-1.5-0.3-2.2H13.4z M4.6,6.5h-4C0.4,7.3,0.3,8,0.3,8.8
                    c0,0.7,0.1,1.5,0.3,2.2h4c-0.1-0.8-0.1-1.5-0.1-2.2C4.5,8,4.5,7.3,4.6,6.5L4.6,6.5z M7.1,16c0.6,1,1.2,1.5,1.9,1.5
                    c0.7,0,1.3-0.5,1.9-1.5c0.6-1,1-2.3,1.2-3.9H5.9C6.2,13.7,6.6,15,7.1,16z M14.8,15.2c1-0.9,1.7-1.9,2.2-3.1h-3.8
                    c-0.3,2.2-0.9,3.8-1.8,5C12.7,16.7,13.8,16.1,14.8,15.2z M3.2,15.2c1,0.9,2.1,1.5,3.3,1.9c-0.8-1.2-1.4-2.8-1.8-5H0.9
                    C1.5,13.3,2.2,14.4,3.2,15.2z" />
                </svg>
              </div>
              <div class="navbar__languages-text">
          <?php $oLanguage = new Language();
                $LanguageAry = $oLanguage->languageAry;
                if(array_key_exists(__LANGUAGE, $LanguageAry)){ echo $LanguageAry[__LANGUAGE];} ?>
              </div>
            </div>
            <div class="navbar__languages-wrap">
              <ul class="navbar__languages-block">
                <li class="navbar__languages-item">
                  <div class="navbar__languages-title">Greater China</div>
                  <ul class="navbar__languages-list">
              <?php foreach($__LanguageAry as $forLangKey=> $forLangAry){
                      echo "<li><a class='navbar__languages-link' href='".HOMEPAGE."?lang=".$forLangAry["code"]."'>".$forLangAry["lang_name"]."</a></li>";
                    } ?>
                  </ul>
                </li>
              </ul>
              <div class="navbar__languages-btn"><?php echo $__dataAry['btn_close'];?></div>
            </div>
          </div>
        </div>
        <div class="navbar__actions-icon">
          <a class="navbar__icon" href="contact-us.php" aria-label="<?php echo $__dataAry['contact_page']['title'];?>">
            <svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
            viewBox="0 0 21 17" style="enable-background:new 0 0 21 17;" xml:space="preserve">
              <g>
                <path style="fill-rule:evenodd;clip-rule:evenodd;" d="M19.3,15c0,0.2-0.2,0.4-0.4,0.4h-17c-0.2,0-0.4-0.2-0.4-0.4
                  V6.1C1.8,6.4,2,6.6,2.3,6.9C4,8.1,5.7,9.4,7.3,10.8c0.9,0.7,1.9,1.6,3.1,1.6h0h0c1.2,0,2.3-0.9,3.1-1.6c1.6-1.3,3.3-2.6,4.9-3.9
                  c0.3-0.2,0.6-0.5,0.8-0.8V15z M19.3,2.8c0,1-0.9,2.3-1.7,2.9c-1.6,1.2-3.1,2.4-4.6,3.7c-0.6,0.5-1.7,1.6-2.5,1.6h0h0
                  c-0.8,0-1.9-1.1-2.5-1.6C6.3,8.1,4.8,6.9,3.2,5.7c-1-0.8-1.7-2-1.7-3.3C1.5,2.2,1.7,2,1.9,2h17C19.4,2,19.3,2.5,19.3,2.8z
                    M20.8,2.4c0-1-0.8-1.9-1.9-1.9h-17C0.9,0.6,0,1.4,0,2.4V15c0,1,0.8,1.9,1.9,1.9h17c1,0,1.9-0.8,1.9-1.9V2.4z"/>
              </g>
            </svg><span class="navbar__icon-text"><?php echo $__dataAry['contact_page']['title'];?></span></a>
        </div>
      </div>
    </div>
  </div>
</header>