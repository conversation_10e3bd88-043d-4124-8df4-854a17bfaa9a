<?php
include("top.php") ?>
<!DOCTYPE html>
<html lang="<?php echo (__LANGUAGE != '2')?'zh-Hant-TW':'en';?>">
  <head>
<?php
    include("head.php");
    require_once(LIBRARY_DIR."library/classes/admin/Control.php");
    require_once(LIBRARY_DIR."library/classes/admin/Account.php");
    require_once(LIBRARY_DIR."library/classes/admin/News.php");
    $oDB = new DB(DB_HOST, DB_NAME, DB_USER, DB_PASSWORD, true);
    if (!$oDB->open()) {
      die(CONNECTION_DB_FAILED.$oDB->error());
    }
    $page = trim($_GET["page"]);
    $oNewsList = new NewsList($oDB);
    $oNewsList->getNewsList("1",__LANGUAGE);
    $setpage = 20;
    $oNewsList->setPageSize($setpage);
    $page = intval($page);
    $page = ($page < 1) ? 1 : $page;
    $page = ($page == ENDPAGE || $page > $oNewsList->totalPage)?$oNewsList->totalPage:$page;
    $oNewsList->getPage($page);
    $total = $oNewsList->size();
    $total_page = $oNewsList->totalPage;
    $myURL = "news-release.php?page=";
    $Pre = $page - 1;
    $Nxt = $page + 1;
    $page_list = ceil($page / $setpage) - 1;
    $page_list_pre = ($page_list*$setpage) + 1;
    $page_list_net = ($page_list*$setpage) + $setpage;
    $pre_str = "";
    $nex_str = "";
    $page_str = "";
    if($page != 1 && $page != "") {
      $pre_str = "<a href=\"".$myURL.$Pre."\">上一頁</a>";
    }
    if($total_page != $page) {
      $nex_str = "<a href=\"".$myURL.$Nxt."\">下一頁</a>";
    } ?>
    <title><?php echo $__dataAry['news_page']['title'];?> | <?php echo $__dataAry['company_name'];?></title>
    <meta name="description" content="<?php echo $__dataAry['news_page']['meta_description'];?>">
    <meta name="copyright" content="<?php echo $__dataAry['news_page']['meta_copyright'];?>">
    <meta property="og:type" content="<?php echo $__dataAry['news_page']['meta_og_type'];?>">
    <meta property="og:site_name" content="<?php echo $__dataAry['news_page']['meta_og_site_name'];?>">
    <meta property="og:title" content="<?php echo $__dataAry['news_page']['meta_og_title'];?>">
    <meta property="og:description" content="<?php echo $__dataAry['news_page']['meta_og_description'];?>">
    <meta property="og:url" content="<?php echo $__dataAry['news_page']['meta_og_url'];?>">
    <meta property="og:image" content="<?php echo $__dataAry['news_page']['meta_og_image'];?>">
  </head>
  <body class="<?php echo (__LANGUAGE != '2')?'':'en';?>">
    <?php include("navbar.php");?>
    <nav class="breadcrumb">
      <div class="breadcrumb__block">
        <div class="breadcrumb__item">
          <a class="breadcrumb__link" href="index.html"><?php echo $__dataAry['home'];?></a>
        </div>
        <div class="breadcrumb__item"><?php echo $__dataAry['news_page']['title'];?></div>
      </div>
    </nav>
    <section class="banner">
      <div class="container">
        <h2 class="banner__title wow fadeIn"><?php echo $__dataAry['news_page']['title'];?></h2>
      </div>
    </section>
    <section class="news-release">
      <div class="container">
        <div class="news-release__wrap wow fadeIn">
          <div class="news-release__block">
      <?php for($i = $oNewsList->getInPageStart();$i < $oNewsList->getInPageEnd();$i++) {
		          $ne = $oNewsList->get($i); ?>
              <div class="news-release__item"><span class="news-release__date"><?php echo $ne->getPdate();?></span>
                <a class="news-release__link" href="news.php?seqno=<?php echo $ne->getSeqno();?>">
                  <div class="news-release__title"><?php echo $ne->getSubj();?></div></a>
              </div>
      <?php }
            $oNewsList->clear();?>
          </div>
        </div>
      </div>
      <div class="page">
        <div class="container">
          <form method="get" name="page_form" id="page_form">
            <div class="page__text wow fadeIn">
              <div class="page__text-item">
          <?php echo $__dataAry['news_page']['common_page'].$total.$__dataAry['news_page']['total_page'].'，'.$__dataAry['news_page']['common_page'].$total_page.$__dataAry['news_page']['page'];?>
              </div>
              <div style="display: inline-block;">
          <?php echo $pre_str;?>
              </div>
              <div class="page__text-item">
                <div style="display: inline-block;">
            <?php echo $nex_str;?>
                </div>
          <?php echo $__dataAry['news_page']['go_page'];?>
                <input class="form__input" name="page" type="tel"><?php echo $__dataAry['news_page']['page'];?>	
              </div>
            </div>
            <div class="page__btn wow fadeIn">
              <button class="btn" onlick="javascript:document.page_form.submit();"><?php echo $__dataAry['news_page']['btn_submit'];?></button>
            </div>
          </form>
        </div>
      </div>
    </section>
    <?php include("footer.php");?>
  </body>
</html>