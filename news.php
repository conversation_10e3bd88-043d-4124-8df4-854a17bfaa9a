<?php
include("top.php") ?>
<!DOCTYPE html>
<html lang="<?php echo (__LANGUAGE != '2')?'zh-Hant-TW':'en';?>">
  <head>
    <?php include("head.php");?>
    <title><?php echo $__dataAry['news_page']['title'];?> | <?php echo $__dataAry['company_name'];?></title>
    <meta name="description" content="<?php echo $__dataAry['news_page']['meta_description'];?>">
    <meta name="copyright" content="<?php echo $__dataAry['news_page']['meta_copyright'];?>">
    <meta property="og:type" content="<?php echo $__dataAry['news_page']['meta_og_type'];?>">
    <meta property="og:site_name" content="<?php echo $__dataAry['news_page']['meta_og_site_name'];?>">
    <meta property="og:title" content="<?php echo $__dataAry['news_page']['meta_og_title'];?>">
    <meta property="og:description" content="<?php echo $__dataAry['news_page']['meta_og_description'];?>">
    <meta property="og:url" content="<?php echo $__dataAry['news_page']['meta_og_url'];?>">
    <meta property="og:image" content="<?php echo $__dataAry['news_page']['meta_og_image'];?>">
  </head>
  <body class="<?php echo (__LANGUAGE != '2')?'':'en';?>">
<?php include("navbar.php");
      // ini_set('display_errors','1');
      require_once($_SERVER["DOCUMENT_ROOT"]."/resonac-all/sysconfig/config.php");
      require_once(LIBRARY_DIR."library/classes/admin/Control.php");
      require_once(LIBRARY_DIR."library/classes/admin/Account.php");
      require_once(LIBRARY_DIR."library/classes/admin/News.php");
      $oDB = new DB(DB_HOST, DB_NAME, DB_USER, DB_PASSWORD, true);
      if (!$oDB->open()) {
        die(CONNECTION_DB_FAILED.$oDB->error());
      }
      $seqno = trim($_GET["seqno"]);
      $oNews = new News($oDB);
      if(!$oNews->getFromDb($seqno)){
        echo "<script>window.location.href = 'news-release.php';</script>";
        exit();
      }
      if($oNews->getStatus() != "1" || $oNews->getLang_id() != __LANGUAGE){
        echo "<script>window.location.href = 'news-release.php';</script>";
        exit();
      }
      $oNews->setImgUnit(); ?>
    <nav class="breadcrumb">
      <div class="breadcrumb__block">
        <div class="breadcrumb__item">
          <a class="breadcrumb__link" href="index.php"><?php echo $__dataAry['home'];?></a>
        </div>
        <div class="breadcrumb__item">
          <a class="breadcrumb__link" href="news-release.php"><?php echo $__dataAry['news_page']['title'];?></a>
        </div>
        <div class="breadcrumb__item"><span class="breadcrumb__text"><?php echo $oNews->getSubj();?></span></div>
      </div>
    </nav>
    <section class="news">
      <div class="news__banner">
        <div class="container">
          <div class="news__banner-block wow fadeIn">
            <h2 class="news__banner-title"><?php echo $oNews->getSubj();?></h2>
            <div class="news__banner-date"><?php echo $oNews->getPdate();?></div>
          </div>
        </div>
      </div>
      <div class="news__wrap">
        <div class="container">
          <div class="news__block wow fadeIn">
            <!-- 最新消息內文-->
            <p><?php echo nl2br($oNews->getContent());?><br></p>
            <!-- <img src="images/news/news.jpg" alt="圖片描述"> -->
            <?php for($i=0;$i < 10;$i++) {
                    if($oNews->getImgTagF($i) != ""){
                      echo $oNews->getImgTagF($i);
                    }
                  } ?>
            <!-- 最新消息內文 END-->
          </div>
        </div>
      </div>
      <div class="page">
        <div class="container">
          <div class="page__btn"> 
            <button class="btn" onclick="window.history.back();"><?php echo $__dataAry['news_page']['btn_back'];?></button>
          </div>
        </div>
      </div>
    </section>
    <?php include("footer.php");?>
  </body>
</html>