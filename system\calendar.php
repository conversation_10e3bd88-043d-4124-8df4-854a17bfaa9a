<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>月曆</title>
</head>

<script language="javascript">
  adate = "<?=$_REQUEST['adate']?>";  
</script>

<style type="text/css">
.td_12 {
 text-align:right; 
 font-size:12px;
}
div.row1 {
 text-align : right; 
 font-size : 12px;
 font-weight : bold;
 border : 0px;
}

div.row2 {
 text-align : left; 
 font-size : 12px;
 border : 0px ;
}
</style>

<link href="/css_backend/mystyle.css" rel="stylesheet" type="text/css">
<script type="text/JavaScript" src="<?=DOMAIN_LIBRARY_URL?>js/aiet.js"></script>
<script language="javascript" src="<?=DOMAIN_LIBRARY_URL?>js/calendar.js"></script>
<script type="text/JavaScript" src="<?=DOMAIN_LIBRARY_URL?>js/common.js"></script>

<body onload ="aCalendar= newCalendar()" bgcolor="#eeeeff">
 <table id="control" name="control"  height="5%" width="100%" class="border1">
   <tr style="font-size:12px">
     <td>
        <select id="year" name="year" size="1" style="font-size:12px" onchange="goMonth()">
         </select>年  
     </td>
     <td>
        <select id="month" name="month" size="1" style="font-size:12px" onchange="goMonth()">
         </select>月
     </td>
     <td>
       <div id="aday" name="aday" style="float:right;font-size=12px">2006/12/01</div>
     </td>  
   </tr>
 </table>
 <table height="90%" width="100%" border="1" cellspacing="0" cellpadding="0">
   <tr height="5%" id="tr0"  name="tr0" style="vertical-align:top; text-align=center; font-size:12px">
     <td>星期日</td>
     <td>星期一</td>
     <td>星期二</td>
     <td>星期三</td>
     <td>星期四</td>
     <td>星期五</td>
     <td>星期六</td>
   </tr>
   <tr id="tr1"  name="tr1" >
     <td onmouseover="checkDay(this)" onmouseout="outcolor(this)" onclick="setDay(this)">
         <div class="row1" id="d0" name="d0">0</div>
     </td>
     <td onmouseover="checkDay(this)" onmouseout="outcolor(this)" onclick="setDay(this)">
         <div class="row1" id="d1" name="d1">0</div>
     </td>
     <td onmouseover="checkDay(this)" onmouseout="outcolor(this)" onclick="setDay(this)">
         <div class="row1" id="d2" name="d2">0</div>
     </td>
     <td onmouseover="checkDay(this)" onmouseout="outcolor(this)" onclick="setDay(this)">
         <div class="row1" id="d3" name="d3">0</div>
     </td>
     <td onmouseover="checkDay(this)" onmouseout="outcolor(this)" onclick="setDay(this)">
         <div class="row1" id="d4" name="d4">0</div>
     </td>
     <td onmouseover="checkDay(this)" onmouseout="outcolor(this)" onclick="setDay(this)">
         <div class="row1" id="d5" name="d5">0</div>
     </td>
     <td onmouseover="checkDay(this)" onmouseout="outcolor(this)" onclick="setDay(this)">
         <div class="row1" id="d6" name="d6">0</div>
     </td>
   </tr>
   <tr id="tr2"  name="tr2">
     <td onmouseover="checkDay(this)" onmouseout="outcolor(this)" onclick="setDay(this)">
         <div class="row1" id="d7" name="d7">0</div>
     </td>
     <td onmouseover="checkDay(this)" onmouseout="outcolor(this)" onclick="setDay(this)">
         <div class="row1" id="d8" name="d8">0</div>
     </td>
     <td onmouseover="checkDay(this)" onmouseout="outcolor(this)" onclick="setDay(this)">
         <div class="row1" id="d9" name="d9">0</div>
     </td>
     <td onmouseover="checkDay(this)" onmouseout="outcolor(this)" onclick="setDay(this)">
         <div class="row1" id="d10" name="d10">0</div>
     </td>
     <td onmouseover="checkDay(this)" onmouseout="outcolor(this)" onclick="setDay(this)">
         <div class="row1" id="d11" name="d11">0</div>
     </td>
     <td onmouseover="checkDay(this)" onmouseout="outcolor(this)" onclick="setDay(this)">
         <div class="row1" id="d12" name="d12">0</div>
     </td>
     <td onmouseover="checkDay(this)" onmouseout="outcolor(this)" onclick="setDay(this)">
         <div class="row1" id="d13" name="d13">0</div>
     </td>
   </tr>
   <tr id="tr3"  name="tr3">
     <td onmouseover="checkDay(this)" onmouseout="outcolor(this)" onclick="setDay(this)">
         <div class="row1" id="d14" name="d14">0</div>
     </td>
     <td onmouseover="checkDay(this)" onmouseout="outcolor(this)" onclick="setDay(this)">
         <div class="row1" id="d15" name="d15">0</div>
     </td>
     <td onmouseover="checkDay(this)" onmouseout="outcolor(this)" onclick="setDay(this)">
         <div class="row1" id="d16" name="d16">0</div>
     </td>
     <td onmouseover="checkDay(this)" onmouseout="outcolor(this)" onclick="setDay(this)">
         <div class="row1" id="d17" name="d17">0</div>
     </td>
     <td onmouseover="checkDay(this)" onmouseout="outcolor(this)" onclick="setDay(this)">
         <div class="row1" id="d18" name="d18">0</div>
     </td>
     <td onmouseover="checkDay(this)" onmouseout="outcolor(this)" onclick="setDay(this)">
         <div class="row1" id="d19" name="d19">0</div>
     </td>
     <td onmouseover="checkDay(this)" onmouseout="outcolor(this)" onclick="setDay(this)">
         <div class="row1" id="d20" name="d20">0</div>
     </td>
   </tr>
   <tr id="tr4"  name="tr4">
     <td onmouseover="checkDay(this)" onmouseout="outcolor(this)" onclick="setDay(this)">
         <div class="row1" id="d21" name="d21">0</div>
     </td>
     <td onmouseover="checkDay(this)" onmouseout="outcolor(this)" onclick="setDay(this)">
         <div class="row1" id="d22" name="d22">0</div>
     </td>
     <td onmouseover="checkDay(this)" onmouseout="outcolor(this)" onclick="setDay(this)">
         <div class="row1" id="d23" name="d23">0</div>
     </td>
     <td onmouseover="checkDay(this)" onmouseout="outcolor(this)" onclick="setDay(this)">
         <div class="row1" id="d24" name="d24">0</div>
     </td>
     <td onmouseover="checkDay(this)" onmouseout="outcolor(this)" onclick="setDay(this)">
         <div class="row1" id="d25" name="d25">0</div>
     </td>
     <td onmouseover="checkDay(this)" onmouseout="outcolor(this)" onclick="setDay(this)">
         <div class="row1" id="d26" name="d26">0</div>
     </td>
     <td onmouseover="checkDay(this)" onmouseout="outcolor(this)" onclick="setDay(this)">
         <div class="row1" id="d27" name="d27">0</div>
     </td>
   </tr>
   <tr id="tr5"  name="tr5">
     <td onmouseover="checkDay(this)" onmouseout="outcolor(this)" onclick="setDay(this)">
         <div class="row1" id="d28" name="d28">0</div>
     </td>
     <td onmouseover="checkDay(this)" onmouseout="outcolor(this)" onclick="setDay(this)">
         <div class="row1" id="d29" name="d29">0</div>
     </td>
     <td onmouseover="checkDay(this)" onmouseout="outcolor(this)" onclick="setDay(this)">
         <div class="row1" id="d30" name="d30">0</div>
     </td>
     <td onmouseover="checkDay(this)" onmouseout="outcolor(this)" onclick="setDay(this)">
         <div class="row1" id="d31" name="d31">0</div>
     </td>
     <td onmouseover="checkDay(this)" onmouseout="outcolor(this)" onclick="setDay(this)">
         <div class="row1" id="d32" name="d32">0</div>
     </td>
     <td onmouseover="checkDay(this)" onmouseout="outcolor(this)" onclick="setDay(this)">
         <div class="row1" id="d33" name="d33">0</div>
     </td>
     <td onmouseover="checkDay(this)" onmouseout="outcolor(this)" onclick="setDay(this)">
         <div class="row1" id="d34" name="d34">0</div>
     </td>
   </tr>
   <tr id="tr6"  name="tr6">
     <td onmouseover="checkDay(this)" onmouseout="outcolor(this)" onclick="setDay(this)">
         <div class="row1" id="d35" name="d35">0</div>
     </td>
     <td onmouseover="checkDay(this)" onmouseout="outcolor(this)" onclick="setDay(this)">
         <div class="row1" id="d36" name="d36">0</div>
     </td>
     <td onmouseover="checkDay(this)" onmouseout="outcolor(this)" onclick="setDay(this)">
         <div class="row1" id="d37" name="d37">0</div>
     </td>
     <td onmouseover="checkDay(this)" onmouseout="outcolor(this)" onclick="setDay(this)">
         <div class="row1" id="d38" name="d38">0</div>
     </td>
     <td onmouseover="checkDay(this)" onmouseout="outcolor(this)" onclick="setDay(this)">
         <div class="row1" id="d39" name="d39">0</div>
     </td>
     <td onmouseover="checkDay(this)" onmouseout="outcolor(this)" onclick="setDay(this)">
         <div class="row1" id="d40" name="d40">0</div>
     </td>
     <td onmouseover="checkDay(this)" onmouseout="outcolor(this)" onclick="setDay(this)">
         <div class="row1" id="d41" name="d41">0</div>
     </td>
   </tr>
 </table>
</body>
</html>

<script language="javascript">
<!--

  var aDate = new Date();

  var objY = document.getElementsByName("year")[0];
  var objM = document.getElementsByName("month")[0];


  var tYear = aDate.getFullYear();
  var tMonth = aDate.getMonth()+1;
  var b2Y= tYear-5;
  var k =0;
  for (var i=b2Y;  i<=tYear+5 ; i++){
    objY.options[k] = new Option(i,i);	
    if (i ==tYear) { objY.options[k].selected = true;}
    k++;
  }	
  for (i=0; i<12; i++){
    var k =101+i;	
    s = k.toString();
    s = s.substr(1,2);
    objM.options[i] = new Option(s,s);	
    if (s==tMonth) { objM.options[i].selected = true;}
  }	




  function newCalendar(){
    var objCalendar = new calendar();
    objCalendar.objYearName = 'year';
    objCalendar.objMonthName = 'month';
    objCalendar.objDateName = 'd';
    objCalendar.fillCalendar();
    document.getElementsByName("aday")[0].innerHTML = objCalendar.toDay();
    
    return  objCalendar;
  }
 
  function checkDay(obj){
    var str = obj.childNodes[0].innerHTML;
   if ( str != "&nbsp;") {overcolor(obj); return;}
  }
  
  function goMonth(){
    var yy = document.getElementsByName("year")[0];
    var mm = document.getElementsByName("month")[0];
    y = yy.options[yy.selectedIndex].value;
    m = parseInt(mm.options[mm.selectedIndex].value);
    window.aCalendar.setDays(y,m);
    return;
  } 
  
  function setDay(obj){
    var yy = document.getElementsByName("year")[0];
    var mm = document.getElementsByName("month")[0];
    y = yy.options[yy.selectedIndex].value;
    
    m = parseInt(mm.options[mm.selectedIndex].value);
    
    if (document.all) { // IE
      d = obj.childNodes[0].innerHTML;
    }
    else { // FF
      d = obj.childNodes[1].innerHTML;
    }  
   if (d.match("&nbsp")){ return;}
   d = parseInt(d);
 
   if (m <10) {m ="0"+m.toString();}
   if (d <10) {d ="0"+d.toString();}

    if (window.opener){
     window.opener.document.getElementsByName(adate)[0].value = y+"-"+m+"-"+d;
     window.close();
    } 
  }
  
//-->
</script>
