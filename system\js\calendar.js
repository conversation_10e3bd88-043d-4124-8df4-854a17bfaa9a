var daysInMonth = new Array(31, 28, 31, 30, 31, 30, 31, 31,30, 31, 30, 31);
var MonsInYear = new Array("Jan","Feb","Mar","Apr","May","Jun","Jul","Agu","Sep","Oct","Nov","Dec");
var MonnInYear = new Array("1","2","3","4","5","6","7","8","9","10","11","12");
var chWeek = new Array("日","一","二","三","四","五","六");
var enWeek = new Array("Sun","Mon","Tue","<PERSON>","Thu","Fri","Sta");
var kind ="";
var objID = "";
var y0 = 100; // 往前 -100 
var y1 = 1;   // 往後 + 1
var delimiter ="-"; //日期中間符號


function calendar() {

   if (location.search != "") {
       var p = new Array();
       var p = location.search.replace('?', '').split('&');
       for(var i=0; i <p.length;i++)   {
    	   p[i] = p[i].replace("=","='")+"'";
    	   eval(p[i]); 
    	}
       if (kind == "0") { document.getElementById("cfm").innerHTML = "民國"; }
   }

   var aDate = new Date();
   
   this.year = aDate.getFullYear();
   if (kind != "") { this.year -= 1911; }
   
   this.month = aDate.getMonth() + 1;
   this.date = aDate.getDate();
   this.day = aDate.getDay();

   this.objYearName = 'year';
   this.objMonthName = 'month';
   this.objDateName = 'd';

   this.getDays = getDays;
   this.setDays = setDays;
   this.incYear = incYear;
   this.incMonth = incMonth;
   this.keyYear = keyYear;
   this.keyMonth = keyMonth;
   this.fillCalendar = fillCalendar;
   this.Today = Today();
   this.goMonth = goMonth;
   this.setDays = setDays;
   this.chooseDay = chooseDay;
   this.focusColor = focusColor;

   //-- initial --
   this.fillCalendar();

   if (document.getElementById("today") != null) {
       document.getElementById("today").innerHTML = this.Today;
   }
   
   if (document.getElementById("month").tagName.toLocaleLowerCase() != "input") {
       document.getElementById("month").options[this.month - 1].selected = "true";
   }
   
   document.getElementById("year").value = this.year;
   
   return ;
}



function setDays(yy,mm){
  this.year = yy;
  this.month = mm;
  this.fillCalendar();
  return;
 
}


function isLeapYear(yy){
  return ((0 == yy % 4) && (0 != (yy % 100))) ||(0 == yy % 400) ? true : false;
}

function getDays(){
   var yy = this.year;
   var mm = this.month;
   if (mm == 2) { return isLeapYear(yy)? 29 : 28;}
   else { return daysInMonth[mm-1];} 
   return;     
 }
 
 function fillCalendar(){
 
    
    var days = this.getDays();
    
    
    var aDate = new Date();
    
    var now_Year  = aDate.getFullYear();
    var now_Month = aDate.getMonth()+1;
    var now_Date = aDate.getDate();
    

    aDate.setDate(1);  // 一定要放在 setMonth 之前 ,
                       // 如果 放在 setMonth 後面,會出問題,
                       // 比方說 今天是 3/31 ,行事曆調到 4月 ,因為 4 月只有 30 天, 系統會自動變成 5 月

    var y = (kind !="" ? this.year + 1911:this.year);
    
    aDate.setFullYear(this.year);
    aDate.setMonth(this.month - 1);  // javascript month(0-11) = current month-1;
    var Day1 = aDate.getDay();

   
    for (var i=0;i < 42; i++){
        var obj = document.getElementById(this.objDateName + i);
        obj.innerHTML = "&nbsp;";
        var j = i % 7;
        if (j == 0 || j == 6) {
            obj.className = "holiday";
        }
        else {
            obj.className = "workday";
        }
        
   }
      
    var obj = document.getElementById(this.objDateName +"1");
      
    for (var i=0;i < days; i++){
        var j = i + Day1;

      document.getElementById(this.objDateName + j).innerHTML = i + 1;
        
      obj = document.getElementById(this.objDateName + j);
      obj.parentNode.style.cursor ="pointer";

      if (this.year == now_Year && this.month == now_Month && now_Date == (i + 1)) {
            obj.className = "today";
      }
  }

    return;
 }
 
 function incYear(i){
   var y = eval(this.year);
   y += i;
   this.year = y;
   this.fillCalendar();

   if (document.getElementById(this.objYearName).tagName.toLowerCase() == "input") {
       document.getElementById(this.objYearName).value = this.year;
   }

   return;
 }
 
 function incMonth(i){
   var m = eval(this.month);
   m += i;
   if (m > 12) {
     m = 1;
     this.incYear(1);
   }
   else if ( m < 1) {
     m = 12;
     this.incYear(-1);
 }

 this.month = m;

   this.fillCalendar();
   if (document.getElementById(this.objMonthName).tagName.toLowerCase() == "input") {
       var m = 100 + m;
       document.getElementById(this.objMonthName).value = String(m).substr(1,2);
   }

   return;
 }
 
 
 function keyYear(event){

   var obj = document.getElementById(this.objYearName);

   var y = obj.value;
   
   if (kind != "") {y = parseInt(y) + 1911;}
   
   if (event) { // for IE
     var keycode = event.keyCode;
   }
   else { // for FF
     var keycode = event.which;
   }

   if (keycode ==13 || keycode==9)  {
     if (isNaN(y)) { obj.focus(); obj.select(); alert("年度值不是數字"); return;}
     else if (y < 1900 || y > 9999) { obj.focus(); obj.select(); alert("年度值範圍不符"); return;}
     else { this.year = y; this.fillCalendar();}
     }  
   return;  
 }
 
 function keyMonth(event){

   var obj = document.getElementById(this.objMonthName);
   
   var m = obj.value;

   if (event) { // for IE
     var keycode = event.keyCode;
   }
   else { // for FF
     var keycode = event.which;
   }
   
   if (keycode ==13 || keycode==9)  {
     if (isNaN(m)) { obj.focus(); alert("月份值不是數字"); return;}
     else if (m < 1 || m > 12) { obj.focus(); alert("月份值範圍不符"); return;}
     else { this.month = m; this.fillCalendar();} 
     } 
   return;  
 }
  
 function Today(){
   var aDate = new Date();
   var  now_Year  = aDate.getFullYear();
   var  now_Month = aDate.getMonth()+1;
   var  now_Date = aDate.getDate();
   var  now_Day = aDate.getDay();

   var m = eval(now_Month);
   var d = eval(now_Date);
   if (m <10) {m ="0"+m.toString();}
   if (d <10) {d ="0"+d.toString();}
   
   if (kind=="") 
   {
     return now_Year+ delimiter +m+ delimiter +d+" "+chWeek[now_Day]; // for chinese
     //return d+"/"+m+"/"+now_Year+" "+chWeek[now_Day]; // for 越南
   }
   else
   {
     return (now_Year-1911)+ delimiter +m+ delimiter +d+" "+chWeek[now_Day];
   }
 } 


  function focusColor(obj){
  
   var str = document.all ? obj.childNodes[0].innerHTML:obj.childNodes[1].innerHTML;
   if ( str != "&nbsp;") { overcolor(obj); return;}
  }

  function goMonth() {
  
    var yy = document.getElementById(this.objYearName);
    var mm = document.getElementById(this.objMonthName);
      //var y = eval(yy.options[yy.selectedIndex].value);  // for select object
    y = yy.value - 0;  // for text object
    if (kind == "0") { y += 1911; }
    if (mm.tagName.toLocaleLowerCase() == "input") 
    {
        m = mm.value;
    }
    else
    {
        m = mm.options[mm.selectedIndex].value;
    }
    this.setDays(y, m);
    return;
  }

  function chooseDay(obj){

    var yy = document.getElementById(this.objYearName);
    var mm = document.getElementById(this.objMonthName);
    //var y = yy.options[yy.selectedIndex].value; // for select object
    var y = yy.value; // for text object

    if (mm.tagName.toLocaleLowerCase() == "input") {
        m = mm.value;
    }
    else {
        m = mm.options[mm.selectedIndex].value;
    }

    if (document.all) { // IE
      d = obj.childNodes[0].innerHTML;
    }
    else { // FF
      d = obj.childNodes[1].innerHTML;
    }
    
   if (d.match("&nbsp")){ return;}
   d = eval(d);

   if (d < 10) {d ="0"+d.toString();}

    if (opener != null) // for window.open
    {
       if (opener.document.getElementById(objID) != undefined)
       {
           opener.document.getElementById(objID).value = y+ delimiter +m+ delimiter +d;
       }
       window.close();
       return;
    }
	
    if (parent != null)  // for iframe
    {
      if (parent.document.getElementById(objID) != undefined)
      {
          parent.document.getElementById(objID).value = y+ delimiter +m+ delimiter +d;
      }
       parent.removeIframe('if'+objID);
       return;
    }
    
  }
