	function clearCond()
	{
		var o = document.getElementById("cond").getElementsByTagName("input");
		for(var i=0; i < o.length; i++)
		{
			if (typeof(o[i].type=="text"))
			{
				o[i].value="";
			} 
		}
		
	   o = document.getElementById("cond").getElementsByTagName("select");
		for(var i=0; i < o.length; i++)
		{
				o[i].value="";
		}
		
		f.submit();
	}
	
   function initSort()
   {
		 //-- 用 attributes["aa"] 判斷是否有屬性
		 //--  getAttribute("aa") 讀取屬性的內容
		 //--  setAttribute("aa","1") 設定屬性的內容
	 
  	    //var c0 ="<?=$sort?>".split('_'); ==> 必須放在主機端的程式 php 
		var o = document.body.getElementsByTagName("td");
		for(var i=0; i < o.length; i++)
		{
			if (typeof(o[i].attributes["sort"]) != "undefined") 
			{
				var cc = o[i].innerHTML.split('&nbsp;');
				if (o[i].id ==c0[0])
				{
					if (c0[1] =="1")
					{
						o[i].innerHTML = cc[0]+'&nbsp;'+'&#9650;';
					}
					else{
						o[i].innerHTML = cc[0]+'&nbsp;'+'&#9660;';
					}
					o[i].setAttribute("sort",c0[1]);						
				}
				else
				{
					o[i].innerHTML =cc[0];
				}
				o[i].onclick = function(){toSort(this);};
				o[i].style.cursor="pointer";
				o[i].getElementsByTagName('span')[0].style.textDecoration="underline";
			}
		}
	   
   }
   
	function toSort(obj)
	{
		initSort();
		switch(obj.getAttribute("sort"))
		{
			default:
				cc = obj.innerHTML.split('&nbsp;');
				obj.innerHTML = cc[0]+'&nbsp;'+'&#9650;';
				obj.setAttribute("sort","1");
			break;
			case "1": 
				cc = obj.innerHTML.split('&nbsp;');
				obj.innerHTML = cc[0]+'&nbsp;'+'&#9660;';
				obj.setAttribute("sort","2");
			break;
		}
		f.sort.value =obj.id+'_'+obj.getAttribute("sort");
		f.submit();
	}