  var strcss = " border-style:groove;background-color:#FFFFFF;font-size:12px;display:none;position:absolute; ";
  document.write("<table style="+strcss+" name=\"popwin\" id=\"popwin\" width=\"auto\" height=\"auto\">");
  document.write("<tr><td id=\"poptext\" name=\"poptext\"></td><tr>");
  document.write("</table>");
  var daysofMonth = new Array(31,28,31,30,31,30,31,31,30,31,30,31);
  var aDate = new Date();

function key13(e){
  if (e) {   var keycode = e.keyCode; }
  else {  var keycode = e.which; }
  
  if (keycode ==13) {return true;}
  else { return false ;}
}

//-----------------
 function overcolor(obj){ 
   bgcolor = obj.style.backgroundColor;
   obj.style.backgroundColor ="#FFFF77";
   return;
  }

 function outcolor(obj) { 
  obj.style.backgroundColor = bgcolor; 
  return; 
 }
 
  function getMousePos(e){
   var pos = new Array();
   if (e) {  // IE
      pos[0]= e.clientX+document.documentElement.scrollLeft; 
      pos[1]= e.clientY+document.documentElement.scrollTop; 
   } else {  // FF
      pos[0] = e.layerX+window.pageXOffset; 
      pos[1]= e.layerY+window.pageYOffset; 
   }
   if (document.getElementsByName("popwin")[0]) {
    document.getElementsByName("popwin")[0].style.left = pos[0] ;
    document.getElementsByName("popwin")[0].style.top =  pos[1] ;
   } 
   return pos;
 }
 
//--------------------------------------


  function showHit(txt,disp){
   document.getElementsByName("poptext")[0].innerHTML = txt;
   document.getElementsByName("popwin")[0].style.display = disp ; 
 }



function MM_preloadImages() { //v3.0
  var d=document; if(d.images){ if(!d.MM_p) d.MM_p=new Array();
    var i,j=d.MM_p.length,a=MM_preloadImages.arguments; for(i=0; i<a.length; i++)
    if (a[i].indexOf("#")!=0){ d.MM_p[j]=new Image; d.MM_p[j++].src=a[i];}}
}

function MM_swapImgRestore() { //v3.0
  var i,x,a=document.MM_sr; for(i=0;a&&i<a.length&&(x=a[i])&&x.oSrc;i++) x.src=x.oSrc;
}

function MM_findObj(n, d) { //v4.01
  var p,i,x;  if(!d) d=document; if((p=n.indexOf("?"))>0&&parent.frames.length) {
    d=parent.frames[n.substring(p+1)].document; n=n.substring(0,p);}
  if(!(x=d[n])&&d.all) x=d.all[n]; for (i=0;!x&&i<d.forms.length;i++) x=d.forms[i][n];
  for(i=0;!x&&d.layers&&i<d.layers.length;i++) x=MM_findObj(n,d.layers[i].document);
  if(!x && d.getElementById) x=d.getElementById(n); return x;
}
//---------------------
function MM_swapImage() { //v3.0
  var i,j=0,x,a=MM_swapImage.arguments; document.MM_sr=new Array; for(i=0;i<(a.length-2);i+=3)
   if ((x=MM_findObj(a[i]))!=null){document.MM_sr[j++]=x; if(!x.oSrc) x.oSrc=x.src; x.src=a[i+2];}
}


 function realsize(imgf,width,height){
   url = "../inc/realSize.php?imgf="+imgf;	
   newopen(url,"",width,height);
 }

function clearFile(fname,img){
  document.getElementsByName(fname)[0].value ="";
  document.getElementsByName(img)[0].src ="";
}	

//------ 維護及瀏覽頁籤操作控制函數集 start--------------------


function goform(no,dowhat){
  gets = "?seqno="+no+"&dowhat="+dowhat;
  parent.window.SelectedTabIndex('0',gets); 
}	
//-------------------------------- 
function goBack(inx){
 parent.window.SelectedTabIndex(inx,'?back=Y'); // URL : xxxxlist.php
 document.getElementsByName("dowhat")[0].value = ""; 
 return;	
}

function undo(){
 document.theform.reset();
 return;
}
//------------------------------------- 
function selectedIndex(obj,p_value){
  obj.defaultSelected=0;
  obj.options[0].selected = true;

  for (var i=0; i < obj.length; i++){
    if (obj.options[i].value == p_value ){ 
    	obj.defaultSelected=i;
    	obj.options[i].selected = true;
     	break;
    }
  }     
}	

function actionStatus(e){
   var obj = document.getElementsByName("dowhat")[0];
   if (obj && obj.value=="") {
     if (priv == 0) {
       alert("您無權限編輯資料");
       f.reset();
     } else if (priv & 1) { 
      if (confirm("系統目前為瀏覽狀態,無法輸入資料,是否要新增")) {  f.dowhat.value="add"; f.submit();}
      else { f.reset();}
    }		
   } 
   return ;	 
 }	
//------------------------------------------
//------ 跳頁控制函數 --------------------
function goPage(page,offset){
  var newpage = eval(page)+eval(offset);
  if (newpage < 1 ) { newpage = 1;}
  else if (newpage > f.lastpage.value) {newpage = f.lastpage.value;}
   
  if (newpage == f.page.value ) {
  	return;
  }
  else { 
   f.page.value = newpage ;	
   f.submit();	
  }
}

function gotoPage(){
  p = f.page1.options[f.page1.selectedIndex].value;	
  goPage(p,0);
}

function result(inx){
  if (document.getElementsByName("do")[0].value !="") { 	
   did = did+(msg==""?"失敗":"成功")+"\n"+msg1;
   alert(did);
   if (msg !="") {goBack(inx);} /* 跳回到瀏覽畫面 */
//    if (msg !=""){   /* 保留增修畫面 */
//      if (document.getElementsByName("dowhat")[0].value =="add") {
//         if ( document.getElementsByName("do")[0].value=="save"){	 
//           if (confirm("是否繼續新增")) {  parent.window.SelectedTabIndex('0','?dowhat=add'); return; }
//         }
//      }   
//    } // if (msg !="")
  } // if document.get
  return;
}
//-----------------------------------------------
//--- 日曆控制函數 ----------
function isLeapYear(yy){
  return (((0 == yy % 4) && (0 != (yy % 100))) ||(0 == yy % 400)) ? true : false;
}

 function decodeDate(sDate){
  if (sDate =="") { return false;}
   sDate.replace("/","-");
   try
    {
     var i = sDate.indexOf("-",0);
     var j = sDate.indexOf("-",i+1);
     var k = sDate.length;
     
     yy = sDate.substr(0,i);
     mm = sDate.substring(i+1,j);
     dd = sDate.substring(j+1,k);
     
     if (isNaN(yy) | isNaN(mm) | isNaN(dd)) { result = false;}
     else {
       if (mm < 1 || mm > 12 || dd < 1) {  result = false;}
       else  {
           if (mm == 2) {result = ((dd > 28 && !isLeapYear(yy)) || (dd>29 && isLeapYear(yy)))?false:true;} 
           else if (dd > daysofMonth[mm-1] ) { result= false;}
       }  
       aDate.setFullYear(yy);  
       aDate.setMonth(mm-1);  
       aDate.setMonth(mm-1); // 確保寫入成功 
       aDate.setDate(dd);
    }   
   }  
   catch (e) {
     result = false; 
   }
   finally { return aDate; }
 }
 
	

function addWork(){
  var str ="/system/worksheet/works.php?inx=0"+"<?=$cond?>"+"&day="+f.day.value;	 
  parent.parent.document.getElementsByName("major")[0].src=str;	
}	

 
//--------------------------- 
function removeIframe(oid)
{
	var o = document.getElementById(oid);
	var children = o.parentNode.childNodes;
	for(i=0;i<children.length;i++){
	  if (children[i].id == o.id)
	  {
		  o.parentNode.removeChild(children[i]);
		  return;
	   }
	}
}

function initCalendar()
{
	var o = document.body.getElementsByTagName("img");
	for(var i=0; i < o.length; i++)
	{
		if (typeof(o[i].attributes["calendar"]) != "undefined") 
		{
			o[i].style.cursor="pointer";
			o[i].onclick = function()
			{
				   pos = getMousePos(event);
				   ifrm = document.createElement("iframe"); 
				   ifrm.src ="/calendar.htm?objID="+this.getAttribute('calendar')+"&y0=0&y1=1";
				   ifrm.className ="calendar";
				   ifrm.frameborder = "0";
				   ifrm.scrolling = "no";
				   ifrm.id ="if"+this.getAttribute('calendar');
				   ifrm.style.left=pos[0];
				   this.parentNode.appendChild(ifrm);
			};

		}
	}
}

	
 function openCalendar(){
    var params = openCalendar.arguments;
    var qdate = params[0];
    var inx = 0;
    var app = navigator.appName;
    if (app.match("Internet Explorer")) { // IE
      x = event.screenX;
      y = event.screenY+20;
    } else { //FF
      x = pos[0];
      y = pos[1];
    }  
    calwin=window.open("/system/calendar.html?adate="+qdate+"&inx="+inx,"calendar","left="+x+",top="+y+",height=250,width=350");
    calwin.moveTo(x,y);
    calwin.focus();
}

 function encodeDate(){
   yy = aDate.getFullYear();
   mm = aDate.getMonth() + 1;
   dd = aDate.getDate();
   if (mm < 10) { mm = "0"+mm.toString();}
   if (dd < 10) { dd = "0"+dd.toString();}
   return yy+"-"+mm+"-"+dd;
  
 }
 
 
 
//--------------------------------------- 
//-----ListBox Control---------- 
 function addList(src,trg){
   var srcobj = document.getElementsByName(src)[0];
   var trgobj = document.getElementsByName(trg)[0];
   var l = trgobj.options.length;
   var exsited;
   for (var i=0; i < srcobj.options.length; i++){
    exsited = false;
    if (srcobj.options[i].selected) {
    	for (var j=0; j < l; j++) { 
          if (trgobj.options[j].value == srcobj.options[i].value){
             exsited = true;
             break;	
          }   
         }
        if (exsited) {continue;}  
    	trgobj.options[l] = new Option(srcobj.options[i].innerHTML,srcobj.options[i].value);
    	l++;
    }		
  }	

}
//------------------------------
function rmList(trg){
   var trgobj = document.getElementsByName(trg)[0];
   var len = trgobj.options.length;
   for (var i=len-1; i >-1; i--){
    if (trgobj.options[i].selected) {
    	trgobj.options[i] = null;
    }
   } 
}

function decoration(obj,value){
    obj.style.textDecoration=value;
}

function ShowHide(i){
  var obj = document.getElementsByName("cont")[i];
  var img = document.getElementsByName("img1")[i];

  obj.style.display = obj.style.display =="block"?"none":"block";
  if ( obj.style.display=="none") { 
  	 img.src ="../../img/btn00.gif";
  	 img.alt ="展開內容";
  } else {  
  	 img.src ="../../img/btn00b.gif";
  	 img.alt ="關閉內容";
  }
  return; 	
}
//-----------------------------
function delRecord(i){
  if (flag) {alert("資料處理中,無法接受"); return;}	
  if (confirm("確定要刪除嗎?")){
     f.del.value="Y";
     f.seqno.value=i;      	
     f.submit();
     flag=1;
  }		
  return;
 }	
 
//------------
 function ShowHide(inx){
  var obj = document.getElementsByName('cont')[inx];
  var img = document.getElementsByName('img1')[inx];  
  obj.style.display = obj.style.display =="block"?"none":"block";
  img.src = obj.style.display =="block"?"/img/btn00b.gif":"/img/btn00.gif";
  img.alt = obj.style.display =="block"?"關閉內容":"打開內容";
  return; 	
} 


