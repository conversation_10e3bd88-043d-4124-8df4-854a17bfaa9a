<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInit9439087368d037d0d02da8f029e998cb
{
    public static $prefixLengthsPsr4 = array (
        'P' => 
        array (
            '<PERSON><PERSON><PERSON>ailer\\PHPMailer\\' => 20,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'PHPMailer\\PHPMailer\\' => 
        array (
            0 => __DIR__ . '/..' . '/phpmailer/phpmailer/src',
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInit9439087368d037d0d02da8f029e998cb::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInit9439087368d037d0d02da8f029e998cb::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInit9439087368d037d0d02da8f029e998cb::$classMap;

        }, null, ClassLoader::class);
    }
}
