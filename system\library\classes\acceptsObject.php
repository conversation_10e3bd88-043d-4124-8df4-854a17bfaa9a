<?php

class acceptsObject{
    public $className = "";
    public function __construct(){
        $this->className = get_class($this);
    }
    public function getClassName(){
        return $this->className;
    }
    public function equals($obj){
        return ($this->getClassName()==$obj->getClassName());
    }
    public function getParentClass(){
        return get_parent_class($this);
    }
    public function getClassMethods(){
        $mds = get_class_methods($this);
        sort($mds);
        return $mds;
    }
    public function getClassProperties(){
        return get_class_vars($this);
    }
}
