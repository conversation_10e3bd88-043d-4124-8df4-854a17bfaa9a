<?php

require_once(LIBRARY_DIR."library/classes/utils/Tools.php");
require_once(LIBRARY_DIR."library/classes/utils/ErrorHandler.php");
require_once(LIBRARY_DIR."library/classes/database/DB4PDO.php");
require_once(LIBRARY_DIR."library/classes/acceptsObject.php");

class Authenticator extends acceptsObject{
    static function isAccountLogin($ss){
        session_start();
        if (!isset($_SESSION[$ss])) {
            header("Location:".NO_PRIV_PAGE."?action=time_out");
        }
        //重要的 global $loginAccount
        global $loginAccount;
        $loginAccount = unserialize($_SESSION[$ss]);
    }
    function isMemberLogin($ss){
        session_start();
        $msg="";
        if (!isset($_SESSION[$ss])) {
            $msg="尚未登入會員";
        }
        //重要的 global $loginAccount
        global $loginMember;
        $loginMember = unserialize($_SESSION[$ss]);
        return $msg;
    }
}
