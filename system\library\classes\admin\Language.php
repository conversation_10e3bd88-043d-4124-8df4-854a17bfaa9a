<?php

require_once(LIBRARY_DIR."library/classes/utils/Tools.php");
require_once(LIBRARY_DIR."library/classes/utils/ErrorHandler.php");
require_once(LIBRARY_DIR."library/classes/database/DB4PDO.php");
require_once(LIBRARY_DIR."library/classes/acceptsObject.php");

class Language extends acceptsObject {
	var $languageAry = array();
	var $languageCodeAry = array();
	var $defLang = "";
	
	function __construct(){
		$this->defLangID = 1;
		$this->languageAry = array(1=>"中文", 2=>"English");
		$this->languageCodeAry = array(1=>"zh", 2=>"en");
	}
	function getLang($select_lang_id){
		$result1 = $this->languageAry;
		if($select_lang_id == ""){
			$select_lang_id = $this->defLangID;
		}else{
			if (!array_key_exists($select_lang_id, $result1)) {
				$select_lang_id = $this->defLangID;
			}
		}
		return $select_lang_id;
	}
	function getLangForCode($select_lang_code){
		$select_lang_id = $this->defLangID;
		$result1 = $this->languageCodeAry;
		if($select_lang_code != ""){
			foreach($result1 as $key=>$code){
				if($select_lang_code == $code) $select_lang_id = $key;
			}
		}
		return $select_lang_id;
	}
    function Load_Language($select_lang_id="", $_type=""){
	    $result1 = $this->languageAry;
		$select_lang_id = $this->getLang($select_lang_id);
	    $d1_array= array();
	    $d1_st="";
		$d1_st=$d1_st." <select size=1 name='language' id='language' class='form-control'>";
		foreach ($result1 as $lang_id => $lang_name){
			$selected = $lang_id == $select_lang_id ? 'selected=selected' : '';
			$d1_array[] = array("lang_id"=>$lang_id, "lang_name"=>$lang_name, "code"=>$this->languageCodeAry[$lang_id], "selected"=>$selected );
			$d1_st=$d1_st."<option value='".$lang_id."' ".$selected." >".$lang_name."</option>";
		}
		$d1_st=$d1_st."  </select>";
		if($_type == "array"){
			return $d1_array;
		}
		return $d1_st;
	}
}
?>