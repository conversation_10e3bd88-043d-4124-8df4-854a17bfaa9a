<?php

require_once($_SERVER["DOCUMENT_ROOT"]."/resonac-all/sysconfig/config.php");
require_once(LIBRARY_DIR."library/classes/utils/Tools.php") ;
require_once(LIBRARY_DIR."library/classes/utils/ErrorHandler.php") ;
require_once(LIBRARY_DIR."library/classes/database/DB4PDO.php") ;
require_once(LIBRARY_DIR."library/classes/acceptsObject.php") ;
require_once(LIBRARY_DIR."library/classes/admin/Language.php");
require_once(LIBRARY_DIR."library/classes/admin/Ticket.php");

/******************* Class News *********************/
class News extends DBObject{
    var $tableName = 'NEWS';
    
    public $seqno;	//序號
    public $lang_id;//語系
    public $common_id;//共用id
    //var $kind;	//A公司新聞,B市場活動,C新聞報導
    public $pdate;	//公告日期
    public $abstract;	//標題
    public $content;	//內文
    public $status; 	//狀態,0:下架,1:上架
    public $creator; 	//建立者
    public $img1;
    public $img2;
    public $img3;
    public $img4;
    public $img5;
    public $img6;
    public $img7;
    public $img8;
    public $img9;
    public $img10;
    public $del1;
    public $del2;
    public $del3;
    public $del4;
    public $del5;
    public $del6;
    public $del7;
    public $del8;
    public $del9;
    public $del10;

    var $fileCount;				// 圖檔數量

    public $relatefile = array();
    public $imgtagb = array();
    public $imgtagf = array();

    function __construct($db){
        // $this->DBObject();
        parent::__construct();
        $this->setDb($db);

        $this->fileCount = 10;
    }
    function getTicketNO(){
		$ticket_no = "";
		$heading = "N".date("ymd");
		$oTicket = new Ticket($this->db);
		if ($oTicket->getFromDb("News",$heading)){
			$ticket_no = $oTicket->incTicket("News",$heading);
		}else{
			$ticket_no = $oTicket->newTicket("News",6,$heading);
		}
		return $ticket_no;
	}
    function getFromDb($pk){
        $founded = false ;
        $sql = "SELECT * FROM NEWS WHERE SEQNO = :pk ";
        $paramAry = array();
        $paramAry[':pk'] = $pk;
        $this->db->execute($sql, $paramAry);
        while ($this->db->fetchRow()) {
            $this->setNews($this->db->rowArray);
            $founded = true;
        }
        return $founded;
    }
    function getFromDbByLangID($common_id, $lang_id) {
		$founded = false;
		$sql = "SELECT * FROM NEWS WHERE COMMON_ID = ? AND LANG_ID = ? ";
		$param = array($common_id, $lang_id);
		$this->db->execute($sql, $param);
		while ($this->db->fetchRow()) {
			$this->setNews($this->db->rowArray);				
			$founded = true;
		}		
		return $founded;
	}
    function setNews($pData){
        foreach($pData as $k=>$v) {
            $k=strtolower($k);
            $this->$k=$v;
        }
    }
    //檔案上傳判斷	
	function fileDoing($fileName=''){
		$msg = '';
        for($i=0; $i < $this->fileCount; $i++){
			$temp = 'img'.($i+1);
            $fileName = iconv("UTF-8", "big5", $this->param[$temp]);
			if($_FILES['userfile']['size'][$this->param['lang_id']][$i] > 0){
				if (!move_uploaded_file($_FILES['userfile']['tmp_name'][$this->param['lang_id']][$i], NEWS_DIR.$fileName)){
                    $msg = "上傳圖檔".$_FILES['userfile']['name'][$this->param['lang_id']][$i]."到(".NEWS_DIR.$fileName.")失敗";
				}else{
                    if(file_exists(NEWS_DIR.$this->$temp) && (NEWS_DIR.$this->$temp <> NEWS_DIR)) {
                        unlink(NEWS_DIR.$this->$temp);
                    }
                }
			}
			$delTmp = 'del'.($i+1);
			if($this->$delTmp == 1){
                if(file_exists(NEWS_DIR.$this->$temp) && (NEWS_DIR.$this->$temp <> NEWS_DIR)) {
                    unlink(NEWS_DIR.$this->$temp);
                }
			}
		}
		return $msg;
	}
    function setAdd($param){
        $this->param = $param;
    }
    function add(){
        $status = $this->addRule();
        $msg = "";
        switch ($status) {
            case 0:
                foreach($this->param as $k=>$v){
					$tmp_col[] = strtoupper($k);
					$tmp_dat[] = ":$k";
					$paramAry[":".$k] = $v;
				}
				$columns = join(",", $tmp_col);
				$data = join(",", $tmp_dat);
                $sql = "INSERT INTO {$this->tableName} (".$columns.") VALUES (".$data.") ";
                $rs = $this->db->execute($sql, $paramAry);
                if ($rs < 1) {
                    $msg="新增資料失敗".$this->db->debugSQL($sql, $paramAry);
                } else {
                    $msg = $this->fileDoing();
                }
                break;
            case 1:
                $msg = "圖檔，格式錯誤(ex:gif或jpg檔)！！";
                break ;
            case 2:
                $msg = "公告日期，格式錯誤(ex:2011-01-01)！！";
                break;
        }
        return $msg;
    }
    function addRule(){
        $status = 0;
        if(!strtotime($this->param['pdate'])){
            $status = 2;
        }else{
            for($i=0; $i < $this->fileCount; $i++){
                $temp = 'img'.($i+1);
                if($_FILES['userfile']['size'][$this->param['lang_id']][$i] > 0) {
                    if ($_FILES['userfile']['type'][$this->param['lang_id']][$i] <> "image/gif" && $_FILES['userfile']['type'][$this->param['lang_id']][$i] <> "image/jpeg"
                    && $_FILES['userfile']['type'][$this->param['lang_id']][$i] <> "image/jpg") {
                        $status = 1;
                    }else{
                        $this->param[$temp] = time()."NE_".$this->param['lang_id'].$i.$_FILES['userfile']['name'][$this->param['lang_id']][$i];
                    }
                }
            }
        }
        return $status;
    }
    function setUpdate($param){
        $this->param = $param;
        for($i=0;$i < $this->fileCount;$i++){
            $delTmp = 'del'.($i+1);
            $this->$delTmp = $this->param[$delTmp];
		    unset($this->param[$delTmp]);
        }
    }
    function update(){
        $status = $this->updateRule();
        $msg = "";
        switch($status) {
            case 0:
                foreach($this->param as $k=>$v){
					$tmp_col[]="`".strtoupper($k)."` = :$k ";			
					$paramAry[":".$k] = $v;
				}
				$pkv = join(',', $tmp_col);
                $sql = "UPDATE {$this->tableName} SET ".$pkv." WHERE SEQNO = :pk ";
                $paramAry[':pk'] = $this->seqno;
                $rs = $this->db->execute($sql, $paramAry);
                if ($rs >= 0) {
                    $msg = $this->fileDoing();
                } else {
                    $msg="更新資料失敗";
                }
                break;
            case 1:
                $msg = "圖檔，格式錯誤(ex:gif或jpg檔)！！";
                break;
            case 2:
                $msg = "公告日期，格式錯誤(ex:2011-01-01)！！";
                break;
        }
        return $msg;
    }
    function updateRule(){
        $status = 0;
        if(!strtotime($this->param['pdate'])){
            $status = 2;
        }else{
            for($i=0;$i < $this->fileCount;$i++){
                $temp = 'img'.($i+1);
                $delTmp = 'del'.($i+1);
                $this->param[$temp] = ($this->$delTmp == 1) ? '' : $this->$temp;
                if($_FILES['userfile']['size'][$this->param['lang_id']][$i] > 0) {
                    if ($_FILES['userfile']['type'][$this->param['lang_id']][$i] <> "image/gif" && $_FILES['userfile']['type'][$this->param['lang_id']][$i] <> "image/jpeg"
                    && $_FILES['userfile']['type'][$this->param['lang_id']][$i] <> "image/jpg") {
                        $status = 1;
                    }else{
                        $this->param[$temp] = time()."NE_".$this->param['lang_id'].$i.$_FILES['userfile']['name'][$this->param['lang_id']][$i];
                    }
                }
            }
        }
        return $status;
    }
    function del(){
        $msg = "";
        $sql = "DELETE FROM NEWS WHERE SEQNO = :pk ";
        $paramAry = array();
        $paramAry[':pk'] = $this->seqno;
        $rs = $this->db->execute($sql, $paramAry);
        if ($rs < 1) {
            $msg="刪除資料失敗";
        } else {
            if (file_exists(NEWS_DIR.$this->img1) && (NEWS_DIR.$this->img1 <> NEWS_DIR)) {
                unlink(NEWS_DIR.$this->img1);
            }
            if (file_exists(NEWS_DIR.$this->img2) && (NEWS_DIR.$this->img2 <> NEWS_DIR)) {
                unlink(NEWS_DIR.$this->img2);
            }
            if (file_exists(NEWS_DIR.$this->img3) && (NEWS_DIR.$this->img3 <> NEWS_DIR)) {
                unlink(NEWS_DIR.$this->img3);
            }
            if (file_exists(NEWS_DIR.$this->img4) && (NEWS_DIR.$this->img4 <> NEWS_DIR)) {
                unlink(NEWS_DIR.$this->img4);
            }
            if (file_exists(NEWS_DIR.$this->img5) && (NEWS_DIR.$this->img5 <> NEWS_DIR)) {
                unlink(NEWS_DIR.$this->img5);
            }
            if (file_exists(NEWS_DIR.$this->img6) && (NEWS_DIR.$this->img6 <> NEWS_DIR)) {
                unlink(NEWS_DIR.$this->img6);
            }
            if (file_exists(NEWS_DIR.$this->img7) && (NEWS_DIR.$this->img7 <> NEWS_DIR)) {
                unlink(NEWS_DIR.$this->img7);
            }
            if (file_exists(NEWS_DIR.$this->img8) && (NEWS_DIR.$this->img8 <> NEWS_DIR)) {
                unlink(NEWS_DIR.$this->img8);
            }
            if (file_exists(NEWS_DIR.$this->img9) && (NEWS_DIR.$this->img9 <> NEWS_DIR)) {
                unlink(NEWS_DIR.$this->img9);
            }
            if (file_exists(NEWS_DIR.$this->img10) && (NEWS_DIR.$this->img10 <> NEWS_DIR)) {
                unlink(NEWS_DIR.$this->img10);
            }
        }
        return $msg ;
    }
    function setImgUnit(){
        for($i=0;$i < 10;$i++) {
            $k = $i + 1;
            $imgname = "img".$k;
            $this->relatefile[$i]=($this->$imgname <> "") ? RELATIVE_NEWS_DIR.$this->$imgname : "";
            list($width, $height, $type, $attr) = getimagesize(NEWS_DIR.$this->$imgname);
            $width_f = $width;
            $height_f = $height;
            $width_b = intval($width * 0.5);
            $height_b = intval($height * 0.5);
            // $this->imgtagb[$i]=($this->$imgname <> "") ? "<img src='".$this->relatefile[$i]."' border='0' width='".$width_b."' height='".$height_b."'>" : "" ;
            $this->imgtagb[$i]=($this->$imgname <> "") ? "<img src='".$this->relatefile[$i]."'>" : "" ;
            // $this->imgtagf[$i]=($this->$imgname <> "") ? "<img src='".$this->relatefile[$i]."' border='0' width='".$width_f."' height='".$height_f."'>" : "" ;
            $this->imgtagf[$i]=($this->$imgname <> "") ? "<img src='".$this->relatefile[$i]."'>" : "" ;
        }
    }
    function getStatusName(){
        $status_name = "" ;
        if ($this->status == "1") {
            $status_name = "開啟";
        } elseif ($this->status == "0") {
            $status_name = "<font color='#ff0000'>關閉</font>";
        }
        return $status_name ;
    }
    function setSeqno($s){
        $this->seqno = $s ;
    }
    function setCommon_id($s){
        $this->common_id = $s ;
    }
    function setLang_id($s){
        $this->lang_id = $s ;
    }
    function setPdate($s){
        $this->pdate = $s ;
    }
    function setSubj($s){
        $this->subj = $s ;
    }
    function setContent($s){
        $this->content = $s ;
    }
    function setStatus($s){
        $this->status = $s ;
    }
    function setCreator($s){
        $this->creator = $s ;
    }
    function setImg1($s){
        $this->img1 = $s ;
    }
    function setImg2($s){
        $this->img2 = $s ;
    }
    function setImg3($s){
        $this->img3 = $s ;
    }
    function setImg4($s){
        $this->img4 = $s ;
    }
    function setImg5($s){
        $this->img5 = $s ;
    }
    function setImg6($s){
        $this->img6 = $s ;
    }
    function setImg7($s){
        $this->img7 = $s ;
    }
    function setImg8($s){
        $this->img8 = $s ;
    }
    function setImg9($s){
        $this->img9 = $s ;
    }
    function setImg10($s){
        $this->img10 = $s ;
    }
    function getSeqno(){
        return $this->seqno;
    }
    function getCommon_id(){
        return $this->common_id;
    }
    function getLang_id(){
        return $this->lang_id;
    }
    function getPdate(){
        return $this->pdate;
    }
    function getSubj(){
        return $this->subj;
    }
    function getContent(){
        return $this->content;
    }
    function getStatus(){
        return $this->status;
    }
    function getCreator(){
        return $this->creator;
    }
    function getRelateFile($ind){
        return $this->relatefile[$ind];
    }
    function getImgTagB($ind){
        return $this->imgtagb[$ind];
    }
    function getImgTagF($ind){
        return $this->imgtagf[$ind];
    }
}
/******************* End Class 	News *****************/

/******************* Class NewsList *****************/
class NewsList extends PageVector{
    public $db = null ;

    function __construct($db){
        // $this->PageVector();
        parent::__construct();
        $this->setDb($db);
    }
    function setDb($db){
        $this->db = $db ;
    }
    function getNewsList($status,$lang_id = 1){
        $paramAry = array();
        $sql = "SELECT * FROM NEWS WHERE 1 " ;
        if($status != ""){
            $sql .= " AND STATUS = :status ";
            $paramAry[':status'] = $status;
        }
        if($lang_id != ''){
			$sql.= "AND LANG_ID = :lang_id ";
			$paramAry[':lang_id'] = $lang_id;
		}
        $sql .= " ORDER BY PDATE DESC" ;
        $this->db->execute($sql, $paramAry);
        while ($this->db->fetchRow()) {
            $g = new News($this->db) ;
            $g->setNews($this->db->rowArray);
            $this->add($g);
        }
        $this->db->freeResult();
    }
}
/******************* End Class NewsList *************/
