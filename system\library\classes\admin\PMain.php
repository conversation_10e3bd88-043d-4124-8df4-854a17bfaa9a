<?php

require_once(LIBRARY_DIR."library/classes/utils/Tools.php");
require_once(LIBRARY_DIR."library/classes/utils/ErrorHandler.php");
require_once(LIBRARY_DIR."library/classes/database/DB4PDO.php");
require_once(LIBRARY_DIR."library/classes/acceptsObject.php");
require_once(LIBRARY_DIR."library/classes/admin/Ticket.php");

/******************* Class PMain *********************/
class PMain extends DBObject{
    public $seqno;	 //類別代碼
    public $name;	 //類別名稱
    public $status;
    public $creator;
    public $place;

    function __construct($db){
        // $this->DBObject();
        parent::__construct();
        $this->setDb($db);
    }
    function getFromDb($pk){
        $founded = false;
        $sql = "SELECT * FROM PRODUCT_C WHERE SEQNO = :pk ";
        $paramAry = array();
        $paramAry[':pk'] = $pk;
        $this->db->execute($sql, $paramAry);
        while ($this->db->fetchRow()) {
            $this->setPMain(
                $this->db->rowArray["SEQNO"],
                $this->db->rowArray["NAME"],
                $this->db->rowArray["PLACE"],
                $this->db->rowArray["STATUS"],
                $this->db->rowArray["CREATOR"]
            );
            $founded = true;
        }
        return $founded;
    }
    function setPMain(){
        $n = func_num_args();
        $this->seqno = trim(func_get_arg(0));
        $this->name  = trim(func_get_arg(1));
        $this->place = trim(func_get_arg(2));
        $this->status = trim(func_get_arg(3));
        $this->creator = trim(func_get_arg(4));
    }
    function setAdd(){
        $n = func_num_args();
        $this->name  = trim(func_get_arg(0));
        $this->place      = trim(func_get_arg(1));
        $this->status    = trim(func_get_arg(2));
        $this->creator   = trim(func_get_arg(3));
    }
    function addRule(){
        $status = 0;
        return $status;
    }
    function add(){
        $status = $this->addRule();
        $msg = "";
        switch ($status) {
            case 0:
                $p[0] = mysql_real_escape_string($this->name);
                $p[1] = $this->place;
                $p[2] = $this->status;
                $p[3] = $this->creator;

                $sql = "INSERT INTO PRODUCT_C(NAME,PLACE,STATUS,CREATOR) VALUES
				('".$p[0]."','".$p[1]."','".$p[2]."','".$p[3]."')";
                $rs = $this->db->affectedSQL($sql);
                if ($rs < 1) {
                    $msg="新增產品類別資料失敗";
                } else {
                }
                break;
        }
        return $msg;
    }
    function setUpdate(){
        $n = func_num_args();
        $this->name     = trim(func_get_arg(0));
        $this->status   = trim(func_get_arg(1));
        $this->place	= trim(func_get_arg(2));
        $this->seqno = trim(func_get_arg(3));
    }
    function updateRule(){
        $status = 0;
        return $status;
    }
    function update(){
        $status = $this->updateRule();
        $msg = "";
        switch ($status) {
            case 0:
                $p[0] = mysql_real_escape_string($this->name);
                $p[1] = $this->status;
                $p[2] = $this->place;
                $p[3] = $this->seqno;

                $sql = "UPDATE PRODUCT_C SET NAME='".$p[0]."',STATUS='".$p[1]."',
				PLACE='".$p[2]."' WHERE SEQNO='".$p[3]."'";
                $rs = $this->db->affectedSQL($sql);
                if ($rs < 1) {
                    $msg="修改產品類別資料失敗";
                } else {
                }
                break;
        }
        return $msg;
    }
    function del(){
        $status = $this->delRule();
        $msg = "";
        switch ($status) {
            case 0:
                $sql = "DELETE FROM PRODUCT_C WHERE SEQNO = :pk ";
                $paramAry = array();
                $paramAry[':pk'] = $this->seqno;
                $rs = $this->db->execute($sql, $paramAry);
                if ($rs < 1) {
                    $msg = "刪除產品類別資料失敗!!";
                } else {
                }
                break;
            case 1:
                $msg = "產品類別已設定關聯，不能刪除！！";
                break;
        }
        return $msg;
    }
    function delRule(){
        $status = 0;
        $sql = "SELECT * FROM PRODUCT WHERE PM_SEQ = :pk ";
        $paramAry = array();
        $paramAry[':pk'] = $this->seqno;
        $this->db->execute($sql, $paramAry);
        if ($this->db->fetchRow()) {
            $status = 1;
        }
        return $status;
    }
    function getStatusName(){
        $status_name = "" ;
        if ($this->status == "1") {
            $status_name = "開啟";
        } elseif ($this->status == "0") {
            $status_name = "<font color='#ff0000'>關閉</font>";
        }
        return $status_name ;
    }
    function setName($s){
        $this->name=$s;
    }
    function setStatus($s){
        $this->status=$s;
    }
    function setPlace($s){
        $this->place=$s;
    }
    function setCreator($s){
        $this->creator=$s;
    }
    function getSeqno(){
        return $this->seqno;
    }
    function getName(){
        return $this->name;
    }
    function getStatus(){
        return $this->status;
    }
    function getCreator(){
        return $this->creator;
    }
    function getPlace(){
        return $this->place;
    }
}
/******************* End Class PMain *****************/

/******************* Class PMainList *****************/
class PMainList extends PageVector{
    public $db = null;
    function __construct($db){
        // $this->PageVector();
        parent::__construct();
        $this->setDb($db);
    }
    function setDb($db){
        $this->db = $db;
    }
    function getPMainList($status){
        $paramAry = array();
        $sql = "SELECT * FROM PRODUCT_C WHERE 1 ";
        if ($status != "") {
            $sql .= " AND STATUS = :status ";
            $paramAry[':status'] = $status;
        }
        $sql .= " ORDER BY PLACE";
        $this->db->execute($sql, $paramAry);
        while ($this->db->fetchRow()) {
            $g = new PMain($this->db);
            $g->setPMain(
                $this->db->rowArray["SEQNO"],
                $this->db->rowArray["NAME"],
                $this->db->rowArray["PLACE"],
                $this->db->rowArray["STATUS"],
                $this->db->rowArray["CREATOR"]
            );
            $this->add($g);
        }
    }
}
/******************* End Class PMainList *************/
