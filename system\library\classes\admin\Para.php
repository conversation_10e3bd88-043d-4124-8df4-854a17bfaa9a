<?php

require_once(LIBRARY_DIR."library/classes/utils/Tools.php");
require_once(LIBRARY_DIR."library/classes/utils/ErrorHandler.php");
require_once(LIBRARY_DIR."library/classes/database/DB4PDO.php");
require_once(LIBRARY_DIR."library/classes/acceptsObject.php");

/******************* Class Para *********************/
class Para extends DBObject{
    public $seqno; 	//序號
    public $style; 	//控制項目
    public $content;  	//控制內容

    function __construct($db){
        // $this->DBObject();
        parent::__construct();
        $this->setDb($db);
    }
    function getFromDb($style){
        $founded = false;
        $sql = "SELECT * FROM PARA WHERE STYLE = :style ";
        $paramAry = array();
        $paramAry[':style'] = $style;
        $this->db->execute($sql, $paramAry);
        while ($this->db->fetchRow()) {
            $this->setPara(
                $this->db->rowArray["SEQNO"],
                $this->db->rowArray["STYLE"],
                $this->db->rowArray["CONTENT"]
            );
            $founded = true;
        }
        return $founded;
    }
    function setPara(){
        $n = func_num_args();
        $this->seqno 	= trim(func_get_arg(0));
        $this->style 	= trim(func_get_arg(1));
        $this->content 	= trim(func_get_arg(2));
    }
    function update($content){
        $msg = "";
        $sql = "UPDATE PARA SET CONTENT = :content WHERE SEQNO = :pk ";
        $paramAry = array();
        $paramAry[':content'] = $content;
        $paramAry[':pk'] = $this->seqno;
        $rs = $this->db->execute($sql, $paramAry);
        if ($rs < 1) {
            $msg="更新資料失敗($sql)";
        }
        return $msg;
    }
    function setSeqNO($s){
        $this->seqno = $s;
    }
    function setStyle($s){
        $this->style = $s;
    }
    function setContent($s){
        $this->content = $s;
    }
    function getSeqNO(){
        return $this->seqno;
    }
    function getStyle(){
        return $this->style;
    }
    function getContent(){
        return $this->content;
    }
}
/******************* End Class Para *****************/

/******************* Class ParaList *****************/
class ParaList extends PageVector{
    public $db = null;
    function __construct($db){
        // $this->PageVector();
        parent::__construct();
        $this->setDb($db);
    }
    function setDb($db){
        $this->db = $db;
    }
    function getParaList($style){
        $paramAry = array();
        $sql = "SELECT * FROM PARA WHERE 1 ";
        if ($style != "") {
            $sql .= " AND STYLE = :style ";
            $paramAry[':style'] = $style;
        }
        $sql .= " ORDER BY STYLE";
        $this->db->execute($sql, $paramAry);
        while ($this->db->fetchRow()) {
            $g = new Para($this->db);
            $g->setPara(
                $this->db->rowArray["SEQNO"],
                $this->db->rowArray["STYLE"],
                $this->db->rowArray["CONTENT"]
            );
            $this->add($g);
        }
    }
}
/******************* End Class ParaList *************/
