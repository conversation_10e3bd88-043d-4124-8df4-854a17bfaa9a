<?php

require_once(LIBRARY_DIR."library/classes/utils/Tools.php");
require_once(LIBRARY_DIR."library/classes/utils/ErrorHandler.php");
require_once(LIBRARY_DIR."library/classes/database/DB4PDO.php");
require_once(LIBRARY_DIR."library/classes/acceptsObject.php");
require_once(LIBRARY_DIR."library/classes/admin/PMain.php");
require_once(LIBRARY_DIR."library/classes/admin/Ticket.php");

/******************* Class Product *********************/
class Product extends DBObject{
    public $seqno;
    public $name;
    public $status;
    public $creator;
    public $pm_seq;
    public $img;
    public $imgtagB;
    public $imgtagF;
    public $place;
    public $url_link;
    public $relatefile;
    public $del1;

    function __construct($db){
        // $this->DBObject();
        parent::__construct();
        $this->setDb($db);
    }
    function getFromDb($pk){
        $founded = false;
        $sql = "SELECT * FROM PRODUCT WHERE SEQNO = :pk ";
        $paramAry = array();
        $paramAry[':pk'] = $pk;
        $this->db->execute($sql, $paramAry);
        while ($this->db->fetchRow()) {
            $this->setProduct(
                $this->db->rowArray["SEQNO"],
                $this->db->rowArray["NAME"],
                $this->db->rowArray["CONTENT"],
                $this->db->rowArray["IMG"],
                $this->db->rowArray["URL_LINK"],
                $this->db->rowArray["PLACE"],
                $this->db->rowArray["STATUS"],
                $this->db->rowArray["CREATOR"],
                $this->db->rowArray["PM_SEQ"]
            );
            $founded = true;
        }
        return $founded;
    }
    function setProduct(){
        $n = func_num_args();
        $this->seqno = trim(func_get_arg(0));
        $this->name  = trim(func_get_arg(1));
        $this->content = trim(func_get_arg(2));
        $this->img = trim(func_get_arg(3));
        $this->url_link = trim(func_get_arg(4));
        $this->place  = trim(func_get_arg(5));
        $this->status  = trim(func_get_arg(6));
        $this->creator = trim(func_get_arg(7));
        $this->pm_seq = trim(func_get_arg(8));
    }
    function setAdd(){
        $n = func_num_args();
        $this->name      = trim(func_get_arg(0));
        $this->content    = trim(func_get_arg(1));
        $this->url_link   = trim(func_get_arg(2));
        $this->place  = trim(func_get_arg(3));
        $this->status = trim(func_get_arg(4));
        $this->creator = trim(func_get_arg(5));
        $this->pm_seq = trim(func_get_arg(6));
    }
    function addRule(){
        $status = 0;
        if ($_FILES['userfile']['size'][0] > 0) {	//檢查圖檔是否有誤
            if ($_FILES['userfile']['type'][0] <> "image/gif" && $_FILES['userfile']['type'][0] <> "image/jpeg"
             && $_FILES['userfile']['type'][0] <> "image/jpg") {
                $status = 1;
            }
        }
        return $status;
    }
    function add(){
        $msg = "" ;
        $status = $this->addRule();
        switch ($status) {
            case 0:
                $p[0] = mysql_real_escape_string($this->name);
                $p[1] = mysql_real_escape_string($this->content);
                $p[2] = ($_FILES['userfile']['size'][0] > 0) ? time()."PD".$_FILES['userfile']['name'][0] : "";
                $p[3] = mysql_real_escape_string($this->url_link);
                $p[4] = $this->place;
                $p[5] = $this->status;
                $p[6] = $this->creator;
                $p[7] = $this->pm_seq;

                $sql = "INSERT INTO PRODUCT(NAME,CONTENT,IMG,URL_LINK,PLACE,STATUS,CREATOR,PM_SEQ)
				VALUES ('".$p[0]."','".$p[1]."','".$p[2]."','".$p[3]."','".$p[4]."','".$p[5]."',
				'".$p[6]."','".$p[7]."')";
                $rs = $this->db->affectedSQL($sql);
                if ($rs < 1) {
                    $msg="新增產品資料失敗";
                } else {
                    //上傳圖片
                    if ($_FILES['userfile']['size'][0] > 0) {
                        if (!move_uploaded_file($_FILES['userfile']['tmp_name'][0], PD_DIR.$p[2])) {
                            $msg="上傳圖檔".$_FILES['userfile']['name'][0]."到(".PD_DIR.$p[2].")失敗";
                        }
                    }
                }
                break;
            case 1:
                $msg = "圖檔格式錯誤！！";
                break;
        }
        return $msg;
    }
    function setImgUnit(){
        $this->relatefile=($this->img <> "") ? RELATIVE_PD_DIR.$this->img : "";
        $width = 83;
        $height = 83;
        $this->imgtagB=($this->img <> "") ? "<img src=\"".$this->relatefile."\" width=\"".$width."\" height=\"".$height."\">" : "" ;
        $this->imgtagF=($this->img <> "") ? "<img src=\"".$this->relatefile."\" width=\"".$width."\" height=\"".$height."\">" : "" ;
    }
    function setUpdate(){
        $n = func_num_args();
        $this->name      = trim(func_get_arg(0));
        $this->content    = trim(func_get_arg(1));
        $this->url_link   = trim(func_get_arg(2));
        $this->place  = trim(func_get_arg(3));
        $this->status = trim(func_get_arg(4));
        $this->del1 = trim(func_get_arg(5));
        $this->pm_seq = trim(func_get_arg(6));
        $this->seqno = trim(func_get_arg(7));
    }
    function updateRule(){
        $status = 0;
        if ($_FILES['userfile']['size'][0] > 0) {	//檢查圖檔是否有誤
            if ($_FILES['userfile']['type'][0] <> "image/gif" && $_FILES['userfile']['type'][0] <> "image/jpeg"
             && $_FILES['userfile']['type'][0] <> "image/jpg") {
                $status = 1;
            }
        }
        return $status;
    }
    function update(){
        $status = $this->updateRule();
        $msg = "";
        switch ($status) {
            case 0:
                $oold = new Product($this->db);
                $oold->getFromDb($this->seqno);
                $p[0] = mysql_real_escape_string($this->name);
                $p[1] = mysql_real_escape_string($this->content);
                $p[2] = ($_FILES['userfile']['size'][0] > 0) ? time()."PD".$_FILES['userfile']['name'][0] : (($this->del1 == "1") ? "" : $oold->img);
                $p[3] = mysql_real_escape_string($this->url_link);
                $p[4] = $this->place;
                $p[5] = $this->status;
                $p[6] = $this->pm_seq;
                $p[7] = $this->seqno;
                $sql = "UPDATE PRODUCT SET NAME='".$p[0]."',CONTENT='".$p[1]."',
				IMG='".$p[2]."',URL_LINK='".$p[3]."',PLACE='".$p[4]."',
				STATUS='".$p[5]."',PM_SEQ='".$p[6]."' WHERE SEQNO='".$p[7]."'";
                $rs = $this->db->affectedSQL($sql);
                if ($rs < 1) {
                    $msg="修改產品資料失敗";
                } else {
                    if ($_FILES['userfile']['size'][0] > 0) {
                        if (!move_uploaded_file($_FILES['userfile']['tmp_name'][0], PD_DIR.$p[2])) {
                            $msg="上傳圖檔".$_FILES['userfile']['name'][0]."到(".PD_DIR.$p[2].")失敗";
                        } else {
                            if (file_exists(PD_DIR.$oold->img) && (PD_DIR.$oold->img <> PD_DIR)) {
                                //刪除多餘的檔案
                                unlink(PD_DIR.$oold->img);
                            }
                        }
                    } else {
                        if($this->del1 == "1") { //刪除圖檔
                            if (file_exists(PD_DIR.$oold->img) && (PD_DIR.$oold->img <> PD_DIR)) {
                                unlink(PD_DIR.$oold->img);
                            }
                        }
                    }
                }
                break;
            case 1:
                $msg = "圖檔格式錯誤！！";
                break;
        } return $msg;
    }
    function del(){
        $status = $this->delRule();
        $msg = "";
        switch ($status) {
            case 0:
                $sql = "DELETE FROM PRODUCT WHERE SEQNO = :pk ";
                $paramAry = array();
                $paramAry[':pk'] = $this->seqno;
                $rs = $this->db->execute($sql, $paramAry);
                if ($rs < 1) {
                    $msg = "刪除產品資料失敗!!";
                } else {
                    if (file_exists(PD_DIR.$this->img) && (PD_DIR.$this->img <> PD_DIR)) {
                        unlink(PD_DIR.$this->img);
                    }
                }
                break;
        }
        return $msg;
    }
    function delRule(){
        $status = 0;
        return $status;
    }
    function getStatusName(){
        $status_name = "" ;
        if ($this->status == "1") {
            $status_name = "開啟";
        } elseif ($this->status == "0") {
            $status_name = "<font color='#ff0000'>關閉</font>";
        }
        return $status_name ;
    }
    function getPMainName(){
        $oTemp = new PMain($this->db);
        $oTemp->getFromDb($this->pmain_id);
        return $oTemp->getName();
    }
    function setName($s){
        $this->name=$s;
    }
    function setStatus($s){
        $this->status=$s;
    }
    function setPlace($s){
        $this->place=$s;
    }
    function setCreator($s){
        $this->creator=$s;
    }
    function setPMSeq($s){
        $this->pm_seq=$s;
    }
    function setContent($s){
        $this->content=$s;
    }
    function setUrl($s){
        $this->url_link=$s;
    }
    function getSeqno(){
        return $this->seqno;
    }
    function getName(){
        return $this->name;
    }
    function getStatus(){
        return $this->status;
    }
    function getPlace(){
        return $this->place;
    }
    function getCreator(){
        return $this->creator;
    }
    function getPMSeq(){
        return $this->pm_seq;
    }
    function getImgTagB(){
        return $this->imgtagB;
    }
    function getImgTagF(){
        return $this->imgtagF;
    }
    function getContent(){
        return $this->content;
    }
    function getUrl(){
        return $this->url_link;
    }
    function getImg(){
        return $this->img;
    }
}
/******************* End Class Product *****************/

/******************* Class ProductList *****************/
class ProductList extends PageVector{
    public $db = null;
    function __construct($db){
        // $this->PageVector();
        parent::__construct();
        $this->setDb($db);
    }
    function setDb($db){
        $this->db = $db;
    }
    function getProductList($status, $pm_seq){
        $paramAry = array();
        $sql = "SELECT * FROM PRODUCT WHERE 1 ";
        if ($status != "") {
            $sql .= " AND STATUS = :status ";
            $paramAry[':status'] = $status;
        }
        if ($pm_seq != "") {
            $sql .= " AND PM_SEQ = :pm_seq ";
            $paramAry[':pm_seq'] = $pm_seq;
        }
        $sql .= " ORDER BY PLACE";
        $this->db->execute($sql, $paramAry);
        while ($this->db->fetchRow()) {
            $g = new Product($this->db);
            $g->setProduct(
                $this->db->rowArray["SEQNO"],
                $this->db->rowArray["NAME"],
                $this->db->rowArray["CONTENT"],
                $this->db->rowArray["IMG"],
                $this->db->rowArray["URL_LINK"],
                $this->db->rowArray["PLACE"],
                $this->db->rowArray["STATUS"],
                $this->db->rowArray["CREATOR"],
                $this->db->rowArray["PM_SEQ"]
            );
            $this->add($g);
        }
    }
}
/******************* End Class ProductList *************/
class Product_title extends DBObject{
    public $seqno;
    public $name;
    public $type;
    public $p_id;
    public $place;
    public $creator;

    function __construct($db){
        // $this->DBObject();
        parent::__construct();
        $this->setDb($db);
    }
    function getFromDb($pk){
        $founded = false;
        $sql = "SELECT * FROM PRODUCT_TITLE WHERE SEQNO = :pk ";
        $paramAry = array();
        $paramAry[':pk'] = $pk;
        $this->db->execute($sql, $paramAry);
        while ($this->db->fetchRow()) {
            $this->setProduct_title(
                $this->db->rowArray["SEQNO"],
                $this->db->rowArray["NAME"],
                $this->db->rowArray["TYPE"],
                $this->db->rowArray["P_ID"],
                $this->db->rowArray["PLACE"],
                $this->db->rowArray["CREATOR"]
            );
            $founded = true;
        }
        return $founded;
    }
    function setProduct_title(){
        $n = func_num_args();
        $this->seqno = trim(func_get_arg(0));
        $this->name  = trim(func_get_arg(1));
        $this->type = trim(func_get_arg(2));
        $this->p_id = trim(func_get_arg(3));
        $this->place = trim(func_get_arg(4));
        $this->creator = trim(func_get_arg(5));
    }
    function setAdd(){
        $n = func_num_args();
        $this->name  = trim(func_get_arg(0));
        $this->type = trim(func_get_arg(1));
        $this->p_id = trim(func_get_arg(2));
        $this->place = trim(func_get_arg(3));
        $this->creator = trim(func_get_arg(4));
    }
    function addRule(){
        $status = 0;
        return $status;
    }
    function add(){
        $msg = "" ;
        $status = $this->addRule();
        $msg = "";
        switch ($status) {
            case 0:
                $p[0] = $this->name;
                $p[1] = $this->type;
                $p[2] = $this->p_id;
                $p[3] = $this->place;
                $p[4] = $this->creator;

                $sql = "INSERT INTO PRODUCT_TITLE(NAME,TYPE,P_ID,PLACE,CREATOR) VALUES
				('$p[0]','$p[1]','$p[2]','$p[3]','$p[4]')";
                $rs = $this->db->affectedSQL($sql);
                if ($rs < 1) {
                    $msg="新增產品大項資料失敗";
                }
                break;
        }
        return $msg;
    }
    function setUpdate(){
        $n = func_num_args();
        $this->name  = trim(func_get_arg(0));
        $this->type = trim(func_get_arg(1));
        //$this->p_id = trim(func_get_arg(2));
        $this->place = trim(func_get_arg(2));
        $this->seqno = trim(func_get_arg(3));
    }
    function update(){
        $msg = "" ;
        $status = $this->addRule();
        $msg = "";
        switch ($status) {
            case 0:
                $p[0] = $this->name;
                $p[1] = $this->type;
                $p[2] = $this->place;
                $p[3] = $this->seqno;

                $sql = "UPDATE PRODUCT_TITLE SET NAME='$p[0]',TYPE='$p[1]',PLACE='$p[2]'
				WHERE SEQNO='$p[3]'";
                $rs = $this->db->affectedSQL($sql);
                if ($rs < 1) {
                    $msg="修改產品大項資料失敗";
                }
                break;
        }
        return $msg;
    }
    function delRule(){
        $status = 0;
        $sql = "SELECT * FROM PRODUCT_TITLE_SUB WHERE P_TITLE_SEQNO = :pk ";
        $paramAry = array();
        $paramAry[':pk'] = $this->seqno;
        $rs = $this->db->execute($sql, $paramAry);
        $this->db->fetchRow();
        if ($this->db->rowArray["SEQNO"] != "") {
            $status = 1;
        }
        return $status;
    }
    function del(){
        $msg = "" ;
        $status = $this->delRule();
        $msg = "";
        switch ($status) {
            case 0:
                $sql = "DELETE FROM PRODUCT_TITLE WHERE SEQNO = :pk ";
                $paramAry = array();
                $paramAry[':pk'] = $this->seqno;
                $rs = $this->db->execute($sql, $paramAry);
                if ($rs < 1) {
                    $msg="刪除產品大項資料失敗";
                }
                break;
            case 1:$msg="無法刪除，已有相關連資料";
                break;
        }
        return $msg;
    }
    function getTypeName(){
        if ($this->type == "A") {
            $typename = "產品簡介";
        } elseif ($this->type == "B") {
            $typename = "產品規格";
        } elseif ($this->type == "C") {
            $typename = "產品資源";
        } else {
            $typename = "";
        }
        return $typename;
    }
    function getSeqno(){
        return $this->seqno;
    }
    function getName(){
        return $this->name;
    }
    function getType(){
        return $this->type;
    }
    function getPID(){
        return $this->p_id;
    }
    function getPlace(){
        return $this->place;
    }
}
/************product_title end***********/
/************product_title_list start***********/
class Product_titleList extends PageVector{
    public $db = null;
    function __construct($db){
        // $this->PageVector();
        parent::__construct();
        $this->setDb($db);
    }
    function setDb($db){
        $this->db = $db;
    }
    function getProduct_titleList($p_id, $type){
        $paramAry = array();
        $first_item = "";
        $sql = "SELECT * FROM PRODUCT_TITLE WHERE 1 AND P_ID = :pk ";
        if ($type != "") {
            $sql .= " AND TYPE = :type ";
            $paramAry[':type'] = $type;
        }
        $sql .= " ORDER BY PLACE";
        $paramAry[':pk'] = $p_id;
        $this->db->execute($sql, $paramAry);
        $i = 0;
        while ($this->db->fetchRow()) {
            if ($i == 0) {
                $first_item = $this->db->rowArray["SEQNO"];
            }
            $g = new Product_title($this->db);
            $g->setProduct_title(
                $this->db->rowArray["SEQNO"],
                $this->db->rowArray["NAME"],
                $this->db->rowArray["TYPE"],
                $this->db->rowArray["P_ID"],
                $this->db->rowArray["PLACE"],
                $this->db->rowArray["CREATOR"]
            );
            $this->add($g);
            $i++;
        }
        return $first_item;
    }
}
/************product_titleList end**************/
class Product_titlesub extends DBObject{
    public $seqno;
    public $subj;
    public $content;
    public $img;
    public $place;
    public $creator;
    public $T_seqno;
    public $imgtag;
    public $imgtagA;
    public $relatefile;

    function __construct($db){
        // $this->DBObject();
        parent::__construct();
        $this->setDb($db);
    }
    function getFromDb($pk){
        $founded = false;
        $sql = "SELECT * FROM PRODUCT_TITLE_SUB WHERE SEQNO = :pk ";
        $paramAry = array();
        $paramAry[':pk'] = $pk;
        $this->db->execute($sql, $paramAry);
        while ($this->db->fetchRow()) {
            $this->setProduct_titlesub(
                $this->db->rowArray["SEQNO"],
                $this->db->rowArray["SUBJECT"],
                $this->db->rowArray["CONTENT"],
                $this->db->rowArray["IMG"],
                $this->db->rowArray["PLACE"],
                $this->db->rowArray["P_TITLE_SEQNO"],
                $this->db->rowArray["CREATOR"]
            );
            $founded = true;
        }
        return $founded;
    }
    function setProduct_titlesub(){
        $n = func_num_args();
        $this->seqno = trim(func_get_arg(0));
        $this->subj  = trim(func_get_arg(1));
        $this->content = trim(func_get_arg(2));
        $this->img = trim(func_get_arg(3));
        $this->place = trim(func_get_arg(4));
        $this->T_seqno = trim(func_get_arg(5));
        $this->creator = trim(func_get_arg(6));
    }
    function setAdd(){
        $n = func_num_args();
        $this->subj  = trim(func_get_arg(0));
        $this->content = trim(func_get_arg(1));
        $this->img = ($_FILES['userfile']['size'][0] > 0) ? "PDT".$_FILES['userfile']['name'][0] : "";
        $this->place = trim(func_get_arg(2));
        $this->creator = trim(func_get_arg(3));
        $this->T_seqno = trim(func_get_arg(4));
    }
    function addRule(){
        $status = 0;
        if ($_FILES['userfile']['size'][0] > 0) {	//檢查圖檔是否有誤
            if ($_FILES['userfile']['type'][0] <> "image/gif" && $_FILES['userfile']['type'][0] <> "image/pjpeg" &&
             $_FILES['userfile']['type'][0] <> "image/bmp" && $_FILES['userfile']['type'][0] <> "image/png") {
                $status = 1;
            }
        }
        return $status;
    }
    function add(){
        $msg = "" ;
        $status = $this->addRule();
        $msg = "";
        switch ($status) {
            case 0:
                $p[0] = $this->subj;
                $p[1] = $this->content;
                $p[2] = $this->img;
                $p[3] = $this->place;
                $p[4] = $this->T_seqno;
                $p[5] = $this->creator;

                $sql = "INSERT INTO PRODUCT_TITLE_SUB(SUBJECT,CONTENT,IMG,PLACE,P_TITLE_SEQNO,CREATOR) VALUES
				('$p[0]','$p[1]','$p[2]','$p[3]','$p[4]','$p[5]')";
                $rs = $this->db->affectedSQL($sql);
                if ($rs < 1) {
                    $msg="新增產品細項資料失敗";
                } else {
                    if ($_FILES['userfile']['size'][0] > 0) {
                        if (!move_uploaded_file($_FILES['userfile']['tmp_name'][0], PD_DIR.$p[2])) {
                            $msg="上傳圖檔".$_FILES['userfile']['name'][0]."到(".PD_DIR.$p[2].")失敗";
                        }
                    }
                }
                break;
            case 1:$msg="圖檔格式錯誤";
                break;
        }
        return $msg;
    }
    function setImgUnit(){
        $this->relatefile=($this->img <> "") ? RELATIVE_PD_DIR.$this->img : "";
        $this->imgtag=($this->img <> "") ? "<img src='".$this->relatefile."' border='0' alt='".$this->subj."'>" : "" ;
        $this->imgtagA=($this->img <> "") ? "<img src='".$this->relatefile."' border='0' height='64' width='64' alt='".$this->subj."'>" : "" ;
    }
    function setUpdate(){
        $n = func_num_args();
        $this->seqno = trim(func_get_arg(0));
        $this->subj  = trim(func_get_arg(1));
        $this->content = trim(func_get_arg(2));
        $this->img = trim(func_get_arg(3));
        $this->place = trim(func_get_arg(4));
        $this->T_seqno = trim(func_get_arg(5));
    }
    function update(){
        $msg = "" ;
        $status = $this->addRule();
        $msg = "";
        switch ($status) {
            case 0:
                $oold = new Product_titlesub($this->db);
                $oold->getFromDb($this->seqno);
                $p[0] = $this->subj;
                $p[1] = $this->content;
                if ($this->img != "") {
                    $p[2] = ($_FILES['userfile']['size'][0] > 0) ? "PDT".$_FILES['userfile']['name'][0] : "";
                } else {
                    $p[2] = ($_FILES['userfile']['size'][0] > 0) ? "PDT".$_FILES['userfile']['name'][0] : $oold->img;
                }
                $p[3] = $this->place;
                $p[4] = $this->T_seqno;
                $p[5] = $this->seqno;

                $sql = "UPDATE PRODUCT_TITLE_SUB SET SUBJECT='$p[0]',CONTENT='$p[1]',IMG='$p[2]',
				PLACE='$p[3]',P_TITLE_SEQNO='$p[4]' WHERE SEQNO='$p[5]'";
                $rs = $this->db->affectedSQL($sql);
                if ($rs < 1) {
                    $msg="修改產品細項資料失敗";
                } else {
                    if ($this->img != "") {
                        if (file_exists(PD_DIR.$this->img)) {
                            unlink(PD_DIR.$this->img);
                        }
                    }
                    if ($_FILES['userfile']['size'][0] > 0) {
                        if (!move_uploaded_file($_FILES['userfile']['tmp_name'][0], PD_DIR.$p[2])) {
                            $msg="上傳圖檔".$_FILES['userfile']['name'][0]."到(".PD_DIR.$p[2].")失敗";
                        }
                    }
                }
                break;
            case 1:$msg="圖檔格式錯誤";
                break;
        }
        return $msg;
    }
    function del(){
        $msg = "" ;
        $status = $this->addRule();
        $msg = "";
        switch ($status) {
            case 0:
                $sql = "DELETE FROM PRODUCT_TITLE_SUB WHERE SEQNO = :pk ";
                $paramAry = array();
                $paramAry[':pk'] = $this->seqno;
                $rs = $this->db->execute($sql, $paramAry);
                if ($rs < 1) {
                    $msg="刪除產品細項資料失敗";
                } else {
                    if ($this->img != "") {
                        if (file_exists(PD_DIR.$this->img)) {
                            unlink(PD_DIR.$this->img);
                        }
                    }
                }
                break;
        }
        return $msg;
    }
    function getSeqno(){
        return $this->seqno;
    }
    function getSubj(){
        return $this->subj;
    }
    function getContent(){
        return $this->content;
    }
    function getTseqno(){
        return $this->T_seqno;
    }
    function getPlace(){
        return $this->place;
    }
    function getImgTag(){
        return $this->imgtag;
    }
    function getImgTagA(){
        return $this->imgtagA;
    }
    function getImg(){
        return $this->img;
    }
}
/************product_title_list start***********/
class Product_titlesubList extends PageVector{
    public $db = null;
    function __construct($db){
        // $this->PageVector();
        parent::__construct();
        $this->setDb($db);
    }
    function setDb($db){
        $this->db = $db;
    }
    function getProduct_titlesubList($t_seqno){
        $sql = "SELECT * FROM PRODUCT_TITLE_SUB WHERE 1 AND P_TITLE_SEQNO = :pk ";
        $sql .= " ORDER BY PLACE";
        $paramAry = array();
        $paramAry[':pk'] = $t_seqno;
        $this->db->execute($sql, $paramAry);
        while ($this->db->fetchRow()) {
            $g = new Product_titlesub($this->db);
            $g->setProduct_titlesub(
                $this->db->rowArray["SEQNO"],
                $this->db->rowArray["SUBJECT"],
                $this->db->rowArray["CONTENT"],
                $this->db->rowArray["IMG"],
                $this->db->rowArray["PLACE"],
                $this->db->rowArray["P_TITLE_SEQNO"],
                $this->db->rowArray["CREATOR"]
            );
            $this->add($g);
        }
    }
}
/************product_titleList end**************/
