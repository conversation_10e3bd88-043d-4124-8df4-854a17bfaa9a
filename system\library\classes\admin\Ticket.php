<?php
require_once(LIBRARY_DIR."library/classes/utils/Tools.php");
require_once(LIBRARY_DIR."library/classes/utils/ErrorHandler.php");
require_once(LIBRARY_DIR."library/classes/database/DB4PDO.php");
require_once(LIBRARY_DIR."library/classes/acceptsObject.php");

/******************* Class Ticket *********************/
class Ticket extends DBObject {
	var $tableName = 'TICKET';

	var $name; 			//名稱
	var $heading; 		//前置字元,單據代碼＋年月日
	var $no;  			//單據號碼
	var $weight;  		//權位值
	var $ticket_no;
	
	
	function __construct($db) {
		// $this->DBObject();
		$this->setDb($db);		
	}
	
	function getFromDb($name, $heading) {
		$founded = false;
		$sql = "SELECT * FROM {$this->tableName} WHERE NAME =? AND HEADING =? ";

		$param = array($name, $heading);
		$this->db->execute($sql, $param);

		while ($this->db->fetchRow()) {
			$this->setData($this->db->rowArray);				
			$founded = true;
		}		
		return $founded;
	}
	
	function setData($pData){
		foreach($pData as $k=>$v){
			$k=strtolower($k);
			$this->$k=$v;
		}
		$this->ticket_no = $this->heading.substr(str_repeat("0",$this->weight).$this->no,-($this->weight));
	}
	
	function getData($varName, $charChk=true){
		if(!isset($this->$varName)){return "";}
		else if( !is_array($this->$varName) && strlen($this->$varName) <= 0  ){return "";}
		else if($charChk){
			return $this->chkHtmlSpecialChars($this->$varName);
		}
		else return $this->$varName;
	}
	
	function chkHtmlSpecialChars($str=""){
		if (is_array($str)){ 
			foreach($str as $key => $val){
				$str[$key] = $this->chkHtmlSpecialChars($val);
			}return $str;
		}$str = htmlspecialchars($str);
		return $str;
	}
	
	function newTicket($name, $weight, $heading) {
		$msg = $columns = $data ="";
	    $no = 1;
		$pAry = array(
			"name"=> $name,
			"weight"=> $weight,
			"heading"=> $heading,
			"no"=> $no
		);
		
		foreach($pAry as $k=>$v){
			$tmp_col[] = strtoupper($k);
			$tmp_dat[] = ":$k";
			$paramAry[":".$k] = $v;
		}
				
		$columns = join(",", $tmp_col);
		$data = join(",", $tmp_dat);
				
		$sql = "INSERT INTO {$this->tableName} (".$columns.") VALUES (".$data.") ";
				
		// echo $sql.'<br>';
				
		$rs = $this->db->execute($sql, $paramAry);

		if ($rs < 1){  
			$msg = "新增單據資料失敗($sql)";
			$this->ticket_no = "-1";
		}else{
			$this->getFromDb($name, $heading);
		}

		return $this->ticket_no;		
	}

	function incTicket($name, $heading){
		$msg = $pkv = "";
		
		$pAry = array(
			"name"=> $name,
			"heading"=> $heading,
			"no" => $this->no + 1
		);
		
		foreach($pAry as $k=>$v){
			$tmp_col[]="`".strtoupper($k)."` = :$k ";			
			$paramAry[":".$k] = $v;		
		}
		$pkv = join(',', $tmp_col);
				
		$sql = "UPDATE {$this->tableName} SET ".$pkv." WHERE HEADING =:heading ";
		$paramAry[':heading'] = $heading;
		// echo $sql."<br>";
				
		$rs = $this->db->execute($sql, $paramAry);

		if ($rs < 1 ){
			$msg = "更新單據號碼資料失敗($sql)";
			$this->ticket_no = "-1";
		}else{
			$this->getFromDb($name, $heading);
		}
		return $this->ticket_no;	
	}

}
/******************* End Class Ticket *****************/

/******************* Class TicketList *****************/
class TicketList extends PageVector {
	var $db = NULL;
	var $tableName = 'TICKET';
	
	function __construct($db) {
		$this->PageVector();		
		$this->setDb($db);
	}
	
	function setDb($db) {$this->db = $db;}
	
	//計數總筆數	
	function getCount_Query(){
		$total = 0;
		if($this->count_sql != ""){
			$count_rs = $this->db->execute($this->count_sql, $this->param);

			if($count_rs){
				if($this->db->fetchRow()){
					$total=$this->db->rowArray[0];
				}
			}
		}
		return $total;
	}
	
	//GET DATA
	function getData_Query($limit_s="", $limit_e=""){
		if($this->select_sql != ""){
			
			$limit_s = intval($limit_s);
			$limit_e = intval($limit_e);
			if($limit_e > 0){
				$this->select_sql .= " LIMIT " ;
				if($limit_s > 0){
					$this->select_sql .= $limit_s." , " ;
				}
				$this->select_sql .= $limit_e."  " ;
			}else{
				if($limit_s > 0){
					$this->select_sql .= "  LIMIT  ".$limit_s."  " ;
				}
			}

			// echo $this->select_sql;
			$this->db->execute($this->select_sql, $this->param);

			while ($this->db->fetchRow()) {
				$g = new Ticket($this->db);
				$g->setData($this->db->rowArray); 
				$this->add($g);
			}$this->db->freeResult();			
		}
	}

	function getTicketList($track){
		$this->count_sql = $this->select_sql = "";
		
		$sql="SELECT {COL} FROM {COL_FROM} WHERE  1 ";
		$COL_COUNT = "COUNT(*)";
		$COL_SELECT = " * ";
		$COL_FROM = $this->tableName;

		if($track !=''){
			$sql.=" AND NAME =? ";
			$param[] = $track;
		}

		$this->param = $param;
		$sql.=" ORDER BY NAME, HEADING DESC";
		
		$this->count_sql = str_replace("{COL}",$COL_COUNT,$sql);
		$this->count_sql = str_replace("{COL_FROM}",$COL_FROM,$this->count_sql);
		
		$this->select_sql = str_replace("{COL}",$COL_SELECT,$sql);
		$this->select_sql = str_replace("{COL_FROM}",$COL_FROM,$this->select_sql);
		
		// echo $this->db->debugSQL($this->select_sql, $this->param);
	}
	
}

?>