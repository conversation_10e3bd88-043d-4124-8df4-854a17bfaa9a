<?php

require_once(LIBRARY_DIR."library/classes/acceptsObject.php");
/***********************************************************
 *  類別：DB
 *	功能：資料庫連線物件
 *  Constructor
 *    DB(string $dbname, string $dbUser, string $dbPwd, boolean $persistent)
 *  Method
 *		open()				開啟資料庫連線
 *		getResult(string $field)	傳回欄位名稱 $field 的值
 *		fetchRow([int i])		移動資料集遊標
 *		query(string $sql)		查詢 $sql 指令
 *		freeResult()			釋放資料集
 *		getConnection()			傳回資料庫連線
 *		error()				傳回錯誤訊息
 *		close()				關閉資料庫連線
 *		toString()			傳回：DBName/UserName/Password
 **********************************************************/
class DB extends acceptsObject
{
    public $host       = "";
    public $user       = "";
    public $password   = "";
    public $database   = "";
    public $persistent = "";
    public $connection = null;
    public $dbSelect   = false;
    public $errorMsg   = "";
    public $resultSet  = null;
    public $queryRow   = 0;
    public $affectdRow = 0;
    public $rowArray ;

    public function __construct($dbHost, $dbName, $dbUser, $dbPwd, $persistent)
    {
        // $this->acceptsObject();
		parent::__construct();
        $this->host       = $dbHost;
        $this->database   = $dbName;
        $this->user       = $dbUser;
        $this->password   = $dbPwd;
        $this->persistent = $persistent;
    }

    public function open()	//utf-8
    {if ($this->persistent) {
        $conn_func = "mysql_pconnect";
    } else {
        $conn_func = "mysql_connect";
    }
        $this->connection = @$conn_func($this->host, $this->user, $this->password);
        if ($this->connection) {
            ini_set(mbstring.internal_encoding, 'UTF-8');
            ini_set(mbstring.http_input, 'UTF-8');
            ini_set(mbstring.http_output, 'UTF-8');
            $this->dbSelect = mysql_select_db($this->database, $this->connection);
            mysql_query("SET NAMES UTF8", $this->connection);
            mysql_query("SET CHARACTER SET UTF8", $this->connection);
            mysql_select_db($this->database);
            mysql_query("SET CHARACTER_SET_CLIENT = UTF8, CHARACTER_SET_CONNECTION = UTF8, CHARACTER_SET_DATABASE = UTF8,
                                            CHARACTER_SET_RESULTS = UTF8,CHARACTER_SET_SERVER = UTF8, COLLATION_CONNECTION = utf8_general_ci,
                                            COLLATION_DATABASE = utf8_general_ci, COLLATION_SERVER = utf8_general_ci, AUTOCOMMIT=1");
            if ($this->dbSelect) {
                return $this->dbSelect;
            } else {
                return false;
            }
        } else {
            echo("connection is false");
            $this->errorMsg = $php_errormsg;
            return false;
        }
    }

    public function getResult($field)
    {
        //$aa = mysql_result($this->resultSet,0,$field);
        $aa =$this->rowArray[$field];
        return $aa;
    }

    public function fetchObject()
    {
        $this->rowArray = mysql_fetch_object($this->resultSet);
        return $this->rowArray;
    }

    public function fetchRow()
    {
        //$this->rowArray =  mysql_fetch_row($this->resultSet);
        $this->rowArray =  mysql_fetch_array($this->resultSet, MYSQL_BOTH);
        return $this->rowArray;
    }

    public function query($sql)
    {
        $this->resultSet = mysql_query($sql, $this->connection);
        if ($this->resultSet) {
            $this->queryRow = mysql_num_rows($this->resultSet);
            return $this->resultSet;
        } else {
            $this->errorMsg = $php_errormsg;
            return false;
        }
    }

    public function affectedSQL($sql)
    {
        //echo("sql=".$sql."...".$this->db->connection."<br>");
        $this->resultSet = mysql_query($sql, $this->connection);
        if ($this->resultSet) {
            $this->affectedRow = mysql_affected_rows($this->connection);
            return $this->resultSet;
        } else {
            $this->errorMsg = $php_errormsg;
            return false;
        }
    }

    public function freeResult()
    {
        return (mysql_free_result($this->resultSet));
    }

    public function getConnection()
    {
        return $this->connection;
    }

    public function getResultSet()
    {
        return $this->resultSet;
    }

    public function error()
    {
        return $this->errorMsg;
    }

    public function close()
    {
        mysql_close($this->connection);
    }

    public function toString()
    {
        return "$this->database/$this->user/$this->password";
    }
}

class DBObject extends acceptsObject
{
    public $db;
    public function __construct()
    {
        // $this->acceptsObject();
		parent::__construct();
    }
    public function setDb($db)
    {
        $this->db = $db;
    }
    public function getDb()
    {
        return $this->db;
    }
}
