<?php

require_once(LIBRARY_DIR."library/classes/acceptsObject.php");

class DB extends acceptsObject {
    var $host       = DB_HOST;
    var $user       = DB_USER;
    var $password   = DB_PASSWORD;
    var $database   = DB_NAME;
    var $persistent = DB_PERSISTENT;
    var $prefix     = DB_PREFIX;
    var $dbSelect   = false;
    var $errorMsg   = "";
    var $resultSet  = NULL;
    var $queryRow   = 0;
    var $rowArray;
    var $oDB;

    function __construct($dbHost = "", $dbName = "", $dbUser = "", $dbPwd = "", $persistent = "", $prefix = "") {
        // $this->acceptsObject();
        parent::__construct();
        if ($dbHost != "") {
            $this->host = $dbHost;
        }
        if ($dbName != "") {
            $this->database = $dbName;
        }
        if ($dbUser != "") {
            $this->user = $dbUser;
        }
        if ($dbPwd != "") {
            $this->password = $dbPwd;
        }
        if ($persistent != "") {
            $this->persistent = $persistent;
        }
        if ($prefix != "") {
            $this->prefix = $prefix;
        }
    }

    function open() {
        try {
            $this->oDB = new PDO(
                'mysql:host='.$this->host.';dbname='.$this->database.';charset=utf8',
                $this->user,
                $this->password
            );
            return $this->oDB;
        } catch (PDOException $exception) {
            $this->errorMsg = $exception->getMessage();
            return false;
        }
    }

    function result_array() {
        $a = array();
        if ($this->queryRow >= 1) {
            $i = 0;
            while ($row = $this->fetchRow()) {
                $a[$i] = $row;
                $i++;
            }
            return $a;
        } else {
            return null;
        }
    }

    function getResult($field) {
        $aa =$this->rowArray[$field];
        return $aa;
    }

    function fetchObject() {
        $this->rowArray = $this->resultSet->fetch(PDO::FETCH_OBJ);
        return $this->rowArray;
    }

    function fetchRow() {
        $this->rowArray = $this->resultSet->fetch(PDO::FETCH_BOTH);
        return $this->rowArray;
    }

    //舊式查詢寫法, 原則上廢棄不用
    function query($sql) {
		try {
            $this->resultSet = $this->oDB->query($sql);
            if ($this->resultSet)
                $this->queryRow = $this->resultSet->rowCount();
            return $this->resultSet;
		} catch (PDOException $exception) {
            $this->errorMsg = $exception->getMessage();
            return false;
		}
    }

    //新式正規PDO查詢寫法
    function execute($sql, $param) {
		try {
            $this->resultSet = $this->oDB->prepare($sql);
            if ($this->resultSet->execute($param)) {
                return $this->queryRow = $this->resultSet->rowCount();
            } else {
                $this->errorMsg = $this->debugSQL($sql, $param)."<br>".$this->resultSet->errorInfo()[2];
                return false;
            }
		} catch (PDOException $exception) {
            $this->errorMsg = $exception->getMessage();
            return false;
		}
    }

    //相容舊程式, 原則上廢棄不用
    function affectedSQL($sql) {
        return $this->query($sql);
    }

    function freeResult() {
        $this->resultSet = null;
        return true;
    }

    function getResultSet() {
        return $this->resultSet;
    }

    function error() {
        return $this->errorMsg;
    }

    function close() {
        $this->oDB = null;
    }

    function getLastID() {
        return $this->oDB->lastInsertId();
    }

    function debugSQL($string, $data) {
        $indexed = $data = (is_array($data)) ? array_values($data): array();  
        foreach ($data as $k => $v) {
            if (is_string($v)) $v = "'$v'";
            if ($indexed) $string = preg_replace('/:\w+/', $v, $string, 1);
            else $string = str_replace("$k", $v, $string);
        }
        return $string;
    }
	
	 public function __sleep(){
        $parmAry = array(
			'host',
			'user',
			'password',
			'database',
			'persistent',
			'prefix',
			'dbSelect',
			'errorMsg',
			'queryRow ',
			'rowArray'
		);
		return $parmAry;
    }

    public function __wakeup() {
        $this->open();
    }
}

class DBObject extends acceptsObject {
    var $db;
    function __construct() {
        // $this->acceptsObject();
        parent::__construct();
    }
    function setDb($db) {$this->db = $db;}
    function getDb() {return $this->db;}
}
?>