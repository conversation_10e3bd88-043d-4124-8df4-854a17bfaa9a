<?php
/**
 * PHPMailer language file.
 * English Version
 */

$PHPMAILER_LANG = array();

$PHPMAILER_LANG["provide_address"]      = 'You must provide at least one ' .
                                          'recipient email address.';
$PHPMAILER_LANG["mailer_not_supported"] = ' mailer is not supported.';
$PHPMAILER_LANG["execute"]              = 'Could not execute: ';
$PHPMAILER_LANG["instantiate"]          = 'Could not instantiate mail function.';
$PHPMAILER_LANG["authenticate"]         = 'SMTP Error: Could not authenticate.';
$PHPMAILER_LANG["from_failed"]          = 'The following From address failed: ';
$PHPMAILER_LANG["recipients_failed"]    = 'SMTP Error: The following ' .
                                          'recipients failed: ';
$PHPMAILER_LANG["data_not_accepted"]    = 'SMTP Error: Data not accepted.';
$PHPMAILER_LANG["connect_host"]         = 'SMTP Error: Could not connect to SMTP host.';
$PHPMAILER_LANG["file_access"]          = 'Could not access file: ';
$PHPMAILER_LANG["file_open"]            = 'File Error: Could not open file: ';
$PHPMAILER_LANG["encoding"]             = 'Unknown encoding: ';
$PHPMAILER_LANG["signing"]              = 'Signing Error: ';
