<?php

/*******************Begin Class*********************************/
define("FATAL", E_USER_ERROR);
define("ERROR", E_USER_WARNING);
define("WARNING", E_USER_NOTICE);
function errorHandler($errno, $errstr, $errfile, $errline){
    switch ($errno) {
        case FATAL:
            echo "<b>嚴重錯誤</b> [$errno] $errstr<br>\n";
            echo "  Fatal error in line ".$errline." of file ".$errfile;
            echo ", PHP ".PHP_VERSION." (".PHP_OS.")<br>\n";
            echo "Aborting...<br>\n";
            exit(1);
            break;
        case ERROR:
            echo "<b>錯誤</b> [$errno] $errstr<br>\n";
            break;
        case WARNING:
            echo "<b>警告</b> [$errno] $errstr<br>\n";
            break;
        default:
            echo "Unkown error type: [$errno] $errstr<br>\n";
            break;
    }
}
function setHandler(){
    set_error_handler("errorHandler");
}
/*******************End Class*********************************/
