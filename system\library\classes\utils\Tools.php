<?php

require_once(LIBRARY_DIR."library/classes/acceptsObject.php");
// require_once($_SERVER['DOCUMENT_ROOT']."/door/library/classes/swift/swift_required.php") ;
// require_once(LIBRARY_DIR."library/classes/mail/class.phpmailer.php");
require_once(LIBRARY_DIR."library/classes/PHPMailer6/vendor/autoload.php");
use PHPMailer\PHPMailer\PHPMailer;

/**************************************************************************
 *	Vector: 可放置物件的容器
 *  Constructor:
 *	Properties:
 *  Methods:
 *		add(Object $v)           加入物件
 *    size()                   傳回物件總筆數
 *    get(int $idx): Object    傳回指定筆數 idx 的物件
 *    getPosition(): int       傳回現在筆數
 *    hasPrevious(): boolean   判斷有沒有前一筆
 *    hasNext(): boolean       判斷有沒有下一筆
 **************************************************************************/
class Vector extends acceptsObject{
    public $items;
    public $icnt = 0;
    public $position = 0;
    public $pre = false;
    public $next = false;
    function __construct(){
        // $this->acceptsObject();
		parent::__construct();
    }
    function add($v){
        $this->items[$this->icnt] = $v;
        $this->icnt++;
    }
    function size(){
        return count($this->items);
    }
    function get($idx){
        $totalsize = $this->size();
        $idx = ($idx < 0) ? 0 : $idx;
        $idx = ($idx < $totalsize) ? $idx : $totalsize;
        $this->position = $idx;
        return $this->items[$idx];
    }
    function clear(){
        if ($this->size()>0) {
            array_splice($this->items, 0, count($this->items));
            $this->icnt = 0;
        }
    }
    function del(){
        if (func_num_args()==1) {
            $i = func_get_arg(0);
        } else {
            $i = $this->position;
        }
        array_splice($this->items, $i, 1);
    }
    function moveTo($p){
        $this->position = $p;
    }
    function getPosition(){
        return $this->position;
    }
    function hasPrevious(){
        return $this->position > 0;
    }
    function hasNext(){
        return $this->position<($this->size()-1);
    }
}
/**************************************************************************
 *	PageVector: 可分頁的 Vector  //繼承自 Vector
 *  Construcrot:
 *	Properties:
 *  Methods:
 *		setPageSize(int $sz)        設定每頁的筆數, 預設值為 1
 *		getPage(int $pidx)				  跳至特定頁數的起頭
 *		getInPage(int $pidx): Object傳回現在頁中特定位置 $pidx 的物件
 *		getPageCount(): int         傳回總頁數
 *		getPageNo(): int            傳回目前頁碼
 *		getPageSize(): int          傳回每頁筆數
 **************************************************************************/
class PageVector extends Vector{
    public $pageSize = 1;
    public $pageNo = 0;
    public $subVector = null;
    public $inPageEnd = 0;
    public $inPageStart = 0;
    public $totalPage = 0;
    function __construct(){
        // $this->Vector();
		parent::__construct();
    }
    function getPage($pidx){
        $this->pageNo   = $pidx;
        $this->inPageStart = ($this->pageSize*($this->pageNo-1) > 0) ? $this->pageSize*($this->pageNo-1) : 0;
        $inPageEnd = $this->inPageStart + $this->pageSize;
        $this->inPageEnd = ($inPageEnd > $this->size()) ? $this->size() : $inPageEnd;
        $this->position       = $this->inPageStart;
    }
    function getInPage($pidx){
        $p = $pidx + ($this->pageSize*($this->pageNo-1));
        return $this->get($p);
    }
    function setPageSize($sz){
        $this->pageSize = $sz;
        $this->totalPage = $this->getPageCount();
    }
    function getPageCount(){
        return ceil($this->size() / $this->pageSize);
    }
    function getPageNo(){
        return $this->pageNo;
    }
    function getPageSize(){
        return $this->pageSize;
    }
    function getInPageStart(){
        return $this->inPageStart;
    }
    function getInPageEnd(){
        return $this->inPageEnd;
    }
}
class StringCtrl extends acceptsObject{
    function __construct(){
        // $this->acceptsObject();
		parent::__construct();
    }
    function showPreFormat($s){
        $s = nl2br($s);
        $s = str_replace("  ", "&nbsp", $s);
        return $s;
    }
    function spc2Nbsp($s){
        $s=$s;
        if (is_null($s)) {
            $s = "&nbsp;";
        }
        if ($s == "") {
            $s = "&nbsp;";
        }
        return $s;
    }
    function crlf2Br($s){
        $s_br = str_replace("\\r\\n", "<br>", $s);
        return $s_br;
    }
    function crlf2Space($s){
        $s_br = str_replace("\\r\\n", "", $s);
        return $s_br;
    }
}
class NameCtrl extends acceptsObject{
    function __construct(){
        // $this->acceptsObject();
		parent::__construct();
    }
    function getDeiveryRuleName($delivery_rule){
        switch ($delivery_rule) {
            case "0":
                $delivery_rule_name = DELIVERY_RULE_0;
                break;
            case "1":
                $delivery_rule_name = DELIVERY_RULE_1;
                break;
            case "2":
                $delivery_rule_name = DELIVERY_RULE_2;
                break;
            default:
                $delivery_rule_name = "&nbsp;";
                break;
        }
        return $delivery_rule_name;
    }
    function getPaymentName($payment){
        switch ($payment) {
            case "0":
                $payment_name = PAYMENT_0;
                break;
            case "1":
                $payment_name = PAYMENT_1;
                break;
            case "2":
                $payment_name = PAYMENT_2;
                break;
            case "3":
                $payment_name = PAYMENT_3;
                break;
            default:
                $payment_name = "&nbsp;";
                break;
        }
        return $payment_name;
    }
}
class DateCtrl extends acceptsObject{
    function __construct(){
        // $this->acceptsObject();
		parent::__construct();
    }
    function strDate7AddDelim($prmDate, $prmDelimiter){
        $trimDate = trim($prmDate);
        $d = substr("0000000".$trimDate, -7);
        $x = $prmDelimiter;
        return substr($d, 0, 3). $x .substr($d, 3, 2). $x .substr($d, -2);
    }
    function strDate8AddDelim($prmDate, $prmDelimiter){
        $trimDate = trim($prmDate);
        $d = substr("00000000".$trimDate, -8);
        $x = $prmDelimiter;
        return substr($d, 0, 4). $x .substr($d, 4, 2). $x .substr($d, -2);
    }
    function strTime4AddDelim($prmTime, $prmDelimiter){
        $trimTime = trim($prmTime);
        $t = substr("0000" & $trimTime, 4);
        $x = $prmDelimiter;
        return substr($t, 0, 2). $x .substr($t, -2);
    }
    function nowDate($type){
        $c=strtoupper($type);
        $t=getdate();
        $mday=$t['mday'];
        $mon=$t['mon'];
        if ($c == "C") {
            $year=$t['year']-1911;
            $getDate=$year*10000+$mon*100+$mday;
            $getDate=substr("0000000".$getDate, -7);
        } else {
            $year=$t['year'];
            $getDate=$year*10000+$mon*100+$mday;
            $getDate=substr("00000000".$getDate, -8);
        }
        return $getDate;
    }
}
class UtilCtrl extends acceptsObject{
    function __construct(){
        // $this->acceptsObject();
		parent::__construct();
    }
    function momeyFormat($money){
        $v = strval(abs($money));
        $new_v = "";
        $i=strlen($v);
        $j=-1;
        while($i >= 1) {
            if ($j >= 2) {
                $new_v = ",".$new_v;
                $j=0;
            } else {
                $j++;
            }
            $new_v = substr($v, $i-1, 1).$new_v;
            $i--;
        }
        return ($money > 0) ? $new_v : "-".$new_v;
    }
    static function QryParaPass(){
        session_start();
        $para_name = unserialize($_SESSION["ss_para_name"]);
        $para_value = unserialize($_SESSION["ss_para_value"]);
        $ary_len = count($para_name);
        $action = $para_value[0]."?";
        for($i=1 ; $i < $ary_len ; $i++) {
            if ($i==1) {
                $action = $action.$para_name[$i]."=".$para_value[$i];
            } else {
                $action = $action."&".$para_name[$i]."=".$para_value[$i];
            }
        }
        return $action;
    }
    function PassDocTitle($title){
        $title = ($title == "") ? COMPANY_NAME."進銷存管理系統" : $title;
        echo("<script language='JavaScript'> ");
        echo("parent.document.title='$title';");
        echo("</script>");
    }
    static function phpmailerContact($name, $company, $tel, $email, $pmain, $subj, $content){
        $sender 		= "聯絡我們";
        $sender_email 	= SENDER_MAIL;
        /************** subject *****************/
        $subject = "聯絡我們";

        /************** message *****************/
        $message  = "<html>" ;
        $message .= "<head>" ;
        $message .= "<title>".$subject."</title>" ;
        $message .= "<meta http-equiv='Content-Type' content='text/html; charset=UTF-8'>" ;
        $message .= "</head>" ;
        $message .= "<body bgcolor='#FFFFFF' leftmargin='0' topmargin='0' marginwidth='0' marginheight='0'>" ;
        $message .= "<br><table width='600' border='0' align='left' cellpadding='0' cellspacing='0'>" ;
        $message .= "<tr><td width='10'>&nbsp;</td><td>姓　　名：".$name."</td></tr>" ;
        $message .= "<tr><td width='10'>&nbsp;</td><td>&nbsp;</td></tr>" ;
        $message .= "<tr><td width='10'>&nbsp;</td><td>公司名稱：".$company."</td></tr>" ;
        $message .= "<tr><td width='10'>&nbsp;</td><td>&nbsp;</td></tr>" ;
        $message .= "<tr><td width='10'>&nbsp;</td><td>連絡電話：".$tel."</td></tr>" ;
        $message .= "<tr><td width='10'>&nbsp;</td><td>&nbsp;</td></tr>" ;
        $message .= "<tr><td width='10'>&nbsp;</td><td>電子郵件：".$email."</td></tr>" ;
        $message .= "<tr><td width='10'>&nbsp;</td><td>&nbsp;</td></tr>" ;
        $message .= "<tr><td width='10'>&nbsp;</td><td>產品類別：".$pmain."</td></tr>" ;
        $message .= "<tr><td width='10'>&nbsp;</td><td>&nbsp;</td></tr>" ;
        $message .= "<tr><td width='10'>&nbsp;</td><td>主　　旨：".$subj."</td></tr>" ;
        $message .= "<tr><td width='10'>&nbsp;</td><td>&nbsp;</td></tr>" ;
        $message .= "<tr><td width='10'>&nbsp;</td><td>內　　容：".$content."</td></tr>" ;
        $message .= "</table>" ;
        $message .= "</body></html>" ;

        $mail = new PHPMailer();
        $mail->SMTPDebug = 0;

        $mail->IsSMTP();
        $mail->CharSet = "utf-8";
        $mail->Encoding = "base64";
        $mail->SMTPAuth = true;
        // new start
        $mail->Host = SMTP_SERVER;
        $mail->Port = 465;
		$mail->SMTPSecure = 'ssl';
        $mail->Username = SMTP_ACCOUNT;
        $mail->Password = SMTP_PASSWORD;
        //phpmailer 若使用5以上，無法順利寄信時加這行
        // $mail->SMTPAutoTLS = false;
        $mail->From = SMTP_ACCOUNT;
        // new end
        $mail->FromName = $sender;
        // 測試用
        // $mail->AddAddress($email);
        $mail->AddAddress("<EMAIL>");
        $mail->AddAddress("<EMAIL>");
        $mail->AddAddress("<EMAIL>");
        $mail->AddAddress("<EMAIL>");

        //$mail->IsHTML(true);
        $mail->Subject = $subject;
        //$mail->Body    = $message;
        $mail->MsgHTML($message);
        $msg = "";
        if(!$mail->send()) {
            $msg = $mail->ErrorInfo;
        }
        return $msg;
    }

    static function phpmailerForgotPassword($email, $reset_token, $account_id) {
        $mail = new PHPMailer();
        $mail->SMTPDebug = 0;

        $mail->IsSMTP();
        $mail->CharSet = "utf-8";
        $mail->Encoding = "base64";
        $mail->SMTPAuth = true;
        $mail->Host = SMTP_SERVER;
        $mail->Port = 465;
        $mail->SMTPSecure = 'ssl';
        $mail->Username = SMTP_ACCOUNT;
        $mail->Password = SMTP_PASSWORD;
        $mail->From = SMTP_ACCOUNT;
        $mail->FromName = "系統管理員";

        $mail->AddAddress($email);

        $reset_url = "https://".$_SERVER['HTTP_HOST']."/resonac-all/backend/reset_password.php?token=".$reset_token;

        $subject = "後台管理系統 - 密碼重設通知";

        $message = "<html><body>";
        $message .= "<h3>密碼重設通知</h3>";
        $message .= "<p>您好，</p>";
        $message .= "<p>您的帳號：<strong>" . htmlspecialchars($account_id) . "</strong></p>";
        $message .= "<p>請點擊以下連結重設密碼：</p>";
        $message .= "<p><a href='" . $reset_url . "' style='color: #0082DA;'>" . $reset_url . "</a></p>";
        $message .= "<p style='color: #ff0000;'>注意：此連結將在30分鐘後失效，請盡快完成密碼重設。</p>";
        $message .= "<p>如果您沒有申請密碼重設，請忽略此郵件。</p>";
        $message .= "</body></html>";

        $mail->Subject = $subject;
        $mail->MsgHTML($message);

        if (!$mail->send()) {
            return $mail->ErrorInfo;
        }
        return "";
    }

    static function maskEmail($email) {
        $parts = explode('@', $email);
        if (count($parts) != 2) return $email;

        $username = $parts[0];
        $domain = $parts[1];

        // 保留前2個字元和後1個字元，中間用*替代
        if (strlen($username) <= 3) {
            $masked_username = str_repeat('*', strlen($username));
        } else {
            $masked_username = substr($username, 0, 2) . str_repeat('*', strlen($username) - 3) . substr($username, -1);
        }

        return $masked_username . '@' . $domain;
    }
    function SQLFilter($str){
        if (!function_exists('str_ireplace')) {
            function str_ireplace($needle, $str, $haystack){
                return preg_replace("/$needle/i", $str, $haystack) ;
            }
        }
        $str = htmlspecialchars(trim($str)) ;
        if ($str == "") {
            return "" ;
        }
        $str = str_replace("--", "&#45;&#45;", $str) ;
        $str = str_replace("=", "&#61;", $str) ;
        $str = str_ireplace("replace", "ｒｅｐｌａｃｅ", $str) ;
        $str = str_ireplace("or", "ｏｒ", $str) ;
        $str = str_ireplace("select", "ｓｅｌｅｃｔ", $str) ;
        $str = str_ireplace("delete", "ｄｅｌｅｔｅ", $str) ;
        $str = str_ireplace("update", "ｕｐｄａｔｅ", $str) ;
        $str = str_ireplace("insert", "ｉｎｓｅｒｔ", $str) ;
        $str = str_ireplace("drop", "ｄｒｏｐ", $str) ;
        return $str ;
    }
}
